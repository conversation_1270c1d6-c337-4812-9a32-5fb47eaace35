#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h> 
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>

// 模拟的XML结果。您可以修改 id 的值来测试不同指令
// 例如，把 id="2" 改成 id="1" 来测试播放音乐的功能
const char *xml_response = "<?xml version=\"1.0\"?>\n"
                           "<nlp>\n"
                           "  <result>\n"
                           "    <confidence>95</confidence>\n"
                           "    <object>\n"
                           "      <cmd id=\"2\">show_pic</cmd>\n"
                           "    </object>\n"
                           "  </result>\n"
                           "</nlp>\n";

int main() {
    // 关键：忽略SIGPIPE信号，防止因客户端断开连接而导致服务器程序退出
    signal(SIGPIPE, SIG_IGN);

    int server_sockfd, client_sockfd;
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_len;

    server_sockfd = socket(AF_INET, SOCK_STREAM, 0);
    
    // 设置地址可重用，避免服务器重启后"Address already in use"的错误
    int on = 1;
    setsockopt(server_sockfd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY; // 监听所有IP地址
    server_addr.sin_port = htons(54321);

    if (bind(server_sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind failed");
        exit(1);
    }
    
    listen(server_sockfd, 5);
    printf("服务器启动成功，将永久运行并等待连接...\n");

    // 无限循环，让服务器一直运行
    while (1) {
        client_len = sizeof(client_addr);
        client_sockfd = accept(server_sockfd, (struct sockaddr *)&client_addr, &client_len);
        if (client_sockfd < 0) {
            perror("accept failed");
            continue; // 如果接受失败，继续等待下一个
        }
        
        printf("\n>>>>>> 接收到来自开发板 %s 的新连接 <<<<<<\n", inet_ntoa(client_addr.sin_addr));

        // 1. 接收 WAV 文件
        FILE *wav_file = fopen("received.wav", "wb");
        if (wav_file != NULL) {
            char buffer[1024];
            int bytes_received;
            printf("步骤 1/2: 正在接收 .wav 文件...\n");
            // 客户端发送完数据并关闭连接后，read 会返回0，循环结束
            while ((bytes_received = read(client_sockfd, buffer, sizeof(buffer))) > 0) {
                 fwrite(buffer, 1, bytes_received, wav_file);
            }
            fclose(wav_file);
            printf("文件接收完毕，已存为 received.wav\n");
        } else {
            perror("fopen received.wav failed");
        }
        
        // 2. 发送 XML 结果 (在同一个连接中)
        printf("步骤 2/2: 正在发送 result.xml 回复...\n");
        write(client_sockfd, xml_response, strlen(xml_response));
        printf("XML 数据发送完毕。\n");

        // 3. 关闭当前连接，准备接受下一个
        close(client_sockfd);
        printf("连接已关闭，服务器继续等待下一个连接...\n");
    }

    close(server_sockfd);
    return 0;
}