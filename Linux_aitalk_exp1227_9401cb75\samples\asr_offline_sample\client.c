// 客户端: 录音5秒, 发送wav文件
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <time.h> // 包含time.h以使用时间戳
#include <errno.h>

#define SERVER_IP "*************" // 服务器的IP地址
#define SERVER_PORT 54321

int init_sock(void)
{
	//创建套接字
	int soc_fd;
	soc_fd = socket(AF_INET, SOCK_STREAM, 0);
	if(-1 == soc_fd)
	{
		perror("socket failed");
		return -1;
	}
	
	//地址可重用
	int on = 1;
	setsockopt(soc_fd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));
	
	//发起连接请求
	printf("向服务器 %s:%d 发起连接请求...\n", SERVER_IP, SERVER_PORT);
	struct sockaddr_in server_addr;
	server_addr.sin_family = AF_INET;
	server_addr.sin_port = htons(SERVER_PORT);
	server_addr.sin_addr.s_addr = inet_addr(SERVER_IP);
	
	socklen_t len = sizeof(struct sockaddr);
	if (connect(soc_fd, (struct sockaddr*)&server_addr, len) == -1)
	{
		perror("connect failed");
        close(soc_fd);
		return -1;
	}
    printf("连接服务器成功！\n");
	return soc_fd;
}

int sendfile(int soc_fd, const char *filename)
{
	//传输文件
	int pcm_fd;
	pcm_fd = open(filename, O_RDONLY);
	if(-1 == pcm_fd)
	{
		perror("open pcm file failed");
		return -1;
	}
    printf("正在发送文件 %s ...\n", filename);

	char buf[1024];
	int ret;
	int sum=0;
	while(1)
	{
		ret = read(pcm_fd, buf, 1024);
		if(-1 == ret)
		{
			perror("read failed");
            close(pcm_fd);
			return -1;
		}
		else if(0 == ret)
		{
			break;
		}
		else
		{
			sum+=ret;
			write(soc_fd, buf, ret);
		}
	}
	
	printf("文件发送完毕，共发送了 %d 字节。\n", sum);
	close(pcm_fd);
	return 0;
}


int main()
{
    printf("客户端程序启动 (非交互模式)。\n");
    
    // 生成一个位于/tmp目录下的唯一文件名，避免污染当前目录
    char pcm_filename[64];
    sprintf(pcm_filename, "/tmp/recording_%ld.pcm", time(NULL));

    // 构建arecord命令，录制5秒音频
    char command[256];
    sprintf(command, "arecord -d5 -c1 -r16000 -fS16_LE -t raw %s", pcm_filename);
    
    printf("开始录音5秒，请说话...\n");
    int ret = system(command);
    if (ret != 0) {
        fprintf(stderr, "录音命令执行失败，返回值: %d\n", ret);
        return 1;
    }
    printf("录音结束，音频已保存为 %s\n", pcm_filename);

    // 初始化网络并连接服务器
    int soc_fd = init_sock();
    if (soc_fd == -1) {
        fprintf(stderr, "无法连接到服务器，请检查服务器是否运行。\n");
        remove(pcm_filename); // 出错时也清理临时文件
        return 1; // 以错误码退出
    }

    // 发送录音文件
    if (sendfile(soc_fd, pcm_filename) != 0) {
        fprintf(stderr, "文件发送失败。\n");
    } else {
        printf("文件发送完毕。正在等待服务器返回识别结果...\n");
        // 优雅地关闭写方向的连接，告知服务器数据已发送完毕
        shutdown(soc_fd, SHUT_WR);

        // 接收服务器返回的识别结果
        char result_buffer[1024] = {0};
        int bytes_read = read(soc_fd, result_buffer, sizeof(result_buffer) - 1);

        if (bytes_read > 0) {
            printf("收到服务器的命令ID: '%s'\n", result_buffer);
            
            // 将命令ID写入一个专门的、临时的通信文件中
            const char* command_filepath = "/tmp/voice_command.txt";
            FILE* command_file = fopen(command_filepath, "w");
            if (command_file) {
                fputs(result_buffer, command_file);
                fclose(command_file);
                printf("命令已成功写入 %s\n", command_filepath);
            } else {
                fprintf(stderr, "错误: 无法在开发板上打开命令文件 %s 进行写入: %s\n", command_filepath, strerror(errno));
            }
        } else if (bytes_read == 0) {
            printf("服务器关闭了连接，但没有发送任何结果。\n");
        } else {
            perror("从服务器接收结果失败");
        }
    }

    // 关闭连接
    close(soc_fd); 
    printf("连接已关闭。\n");

    // 删除临时的录音文件
    remove(pcm_filename);
    printf("临时文件 %s 已删除。\n", pcm_filename);
	
    printf("客户端任务完成，正在退出。\n");
	return 0;
} 