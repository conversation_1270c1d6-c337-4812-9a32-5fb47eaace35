#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>
#include <fcntl.h>
#include <dirent.h>
#include <ctype.h>
// #include <stdbool.h> //不再需要

#define LISTEN_PORT 54322
#define SAVE_DIR "/mnt/hgfs/share/jpg"
#define RESULT_FILE "/mnt/hgfs/share/jpg/result.txt"
#define TEMP_BMP_FILE "/tmp/received_temp.bmp"
// #define POLLING_TIMEOUT_S 15      // 不再需要
// #define POLLING_INTERVAL_MS 500   // 不再需要
// #define NOTIFY_PORT 54323         // 不再需要

// 确保目录存在
void ensure_directory_exists(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == 0) {
            printf("目录 '%s' 已创建。\n", path);
        } else {
            perror("创建目录失败");
        }
    }
}

// 获取目录下最大的JPG文件编号，返回下一个可用的编号
int get_next_filenumber(const char* dir_path) {
    int max_num = 0;
    DIR* d = opendir(dir_path);
    if (!d) {
        // 目录不存在或无法打开，可以安全地从1开始
        return 1;
    }
    
    struct dirent* entry;
    while ((entry = readdir(d)) != NULL) {
        char* jpg_ext = strstr(entry->d_name, ".jpg");
        // 确保文件是.jpg后缀
        if (entry->d_type == DT_REG && jpg_ext && jpg_ext[4] == '\0') {
             int is_numeric = 1;
             char* p;
             // 检查文件名（扩展名前的部分）是否为纯数字
             for (p = entry->d_name; p < jpg_ext; ++p) {
                 if (!isdigit((unsigned char)*p)) {
                     is_numeric = 0;
                     break;
                 }
             }
             if (is_numeric) {
                 int num = atoi(entry->d_name);
                 if (num > max_num) {
                     max_num = num;
                 }
             }
        }
    }
    closedir(d);
    return max_num + 1;
}

// 不再需要 check_recognition_result 函数

// 不再需要 notify_board 函数


int main() {
    // 忽略SIGPIPE信号
    signal(SIGPIPE, SIG_IGN);

    // 在程序开始时，清空旧的识别结果文件
    FILE* f_clear = fopen(RESULT_FILE, "w");
    if (f_clear) {
        fclose(f_clear);
        printf("旧的识别结果文件 %s 已清空。\n", RESULT_FILE);
    } else {
        // 如果文件不存在或无法打开，这不是一个致命错误，
        // 但记录下来有助于调试。
        perror("警告：无法清空识别结果文件");
    }

    ensure_directory_exists(SAVE_DIR);

    int server_sockfd, client_sockfd;
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_len;

    server_sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if(server_sockfd < 0){
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }
    
    int on = 1;
    setsockopt(server_sockfd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(LISTEN_PORT);

    if (bind(server_sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind failed");
        exit(EXIT_FAILURE);
    }
    
    listen(server_sockfd, 5);
    printf("图片接收服务器已启动...\n");

    while (1) {
        client_len = sizeof(client_addr);
        client_sockfd = accept(server_sockfd, (struct sockaddr *)&client_addr, &client_len);
        if (client_sockfd < 0) {
            perror("accept failed");
            continue;
        }
        
        printf("\n>>>>>> 接收到来自 %s 的新连接 <<<<<<\n", inet_ntoa(client_addr.sin_addr));

        // 1. 接收图片
        int temp_fd = open(TEMP_BMP_FILE, O_WRONLY | O_CREAT | O_TRUNC, 0644);
        if (temp_fd != -1) {
            char buffer[4096];
            ssize_t bytes_received;
            while ((bytes_received = read(client_sockfd, buffer, sizeof(buffer))) > 0) {
                write(temp_fd, buffer, bytes_received);
            }
            close(temp_fd);

            // 2. 转换图片
            int next_filenum = get_next_filenumber(SAVE_DIR);
            char final_jpg_path[256];
            sprintf(final_jpg_path, "%s/%d.jpg", SAVE_DIR, next_filenum);
            char command[512];
            sprintf(command, "convert %s %s", TEMP_BMP_FILE, final_jpg_path);
            if (system(command) == 0) {
                printf("图片已转换为 %s\n", final_jpg_path);
            } else {
                fprintf(stderr, "错误: 图片转换失败。\n");
            }
            remove(TEMP_BMP_FILE);
        }

        // 3. 读取本地识别结果
        FILE* result_f = fopen(RESULT_FILE, "r");
        char result_buffer[256] = "Unknown"; // 默认回复 "Unknown"
        if (result_f) {
            char line_buffer[256];
            // 读取最后一行有效内容
            while (fgets(line_buffer, sizeof(line_buffer), result_f)) {
                 line_buffer[strcspn(line_buffer, "\r\n")] = 0;
                 if(strlen(line_buffer) > 0) {
                     strcpy(result_buffer, line_buffer);
                 }
            }
            fclose(result_f);
        }
        
        // 4. 将结果发送回开发板
        printf("读取到结果 '%s'，正在发回开发板...\n", result_buffer);
        if (write(client_sockfd, result_buffer, strlen(result_buffer)) < 0) {
            // 这不是一个致命错误。
            // 客户端可能是一个“即发即忘”的发送者（例如拍照上传功能），
            // 它在发送完图片后会立即关闭连接，而不会等待响应。
            perror("向客户端写回结果失败 (提示)");
        }
        
        // 5. 关闭连接
        close(client_sockfd);
        printf("连接已关闭。\n");
    }

    close(server_sockfd);
    return 0;
} 