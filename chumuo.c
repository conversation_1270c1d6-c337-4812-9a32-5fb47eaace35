#define _XOPEN_SOURCE 700 // 指定 X/Open 标准版本 
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <linux/input.h>
#include <dirent.h>
#include <stdbool.h>
#include <sys/time.h>
#include <sys/ioctl.h>
#include <linux/fb.h>
#include <errno.h>
#include <sys/mman.h>
#include <sys/select.h>

// =================================================================================
// Embedded Font Data (8x8 pixel, row-major, 1-bit per pixel)
// A public domain font.
// =================================================================================
static const unsigned char font8x8_basic[128][8] = {
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0000 (nul) */
  {0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00, 0x18, 0x00},   /* U+0001 */
  {0x66, 0x66, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0002 */
  {0x66, 0x66, 0x7E, 0x66, 0x7E, 0x66, 0x66, 0x00},   /* U+0003 */
  {0x18, 0x3C, 0x60, 0x3C, 0x0C, 0x3C, 0x18, 0x00},   /* U+0004 */
  {0x60, 0x66, 0x0C, 0x18, 0x30, 0x66, 0x06, 0x00},   /* U+0005 */
  {0x3C, 0x66, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00},   /* U+0006 */
  {0x00, 0x18, 0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00},   /* U+0007 */
  {0xFF, 0xE7, 0xE7, 0xFF, 0xE7, 0xE7, 0xFF, 0x00},   /* U+0008 */
  {0x18, 0x3C, 0x66, 0x66, 0x3C, 0x18, 0x18, 0x00},   /* U+0009 */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+000A */
  {0x1C, 0x36, 0x63, 0x41, 0x41, 0x63, 0x36, 0x1C},   /* U+000B */
  {0x0C, 0x18, 0x30, 0x60, 0x60, 0x30, 0x18, 0x0C},   /* U+000C */
  {0x18, 0x00, 0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00},   /* U+000D */
  {0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x18, 0x30, 0x00},   /* U+000E */
  {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00},   /* U+000F */
  {0x00, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x80, 0x00},   /* U+0010 */
  {0x00, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x00, 0x00},   /* U+0011 */
  {0x00, 0x18, 0x3C, 0x66, 0x66, 0x3C, 0x18, 0x00},   /* U+0012 */
  {0x00, 0x3C, 0x18, 0x3C, 0x18, 0x3C, 0x18, 0x00},   /* U+0013 */
  {0x18, 0x3C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00},   /* U+0014 */
  {0x18, 0x18, 0x18, 0x18, 0x3C, 0x18, 0x3C, 0x00},   /* U+0015 */
  {0x3C, 0x66, 0x6E, 0x7E, 0x60, 0x60, 0x00, 0x00},   /* U+0016 */
  {0x18, 0x18, 0x30, 0x30, 0x60, 0x60, 0x00, 0x00},   /* U+0017 */
  {0x00, 0x60, 0x30, 0x18, 0x0C, 0x60, 0x00, 0x00},   /* U+0018 */
  {0x00, 0x0C, 0x18, 0x30, 0x60, 0x0C, 0x00, 0x00},   /* U+0019 */
  {0x00, 0x00, 0x00, 0x7E, 0x00, 0x18, 0x3C, 0x00},   /* U+001A */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+001B */
  {0x00, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},   /* U+001C */
  {0x00, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, 0x18},   /* U+001D */
  {0x00, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x00},   /* U+001E */
  {0x00, 0xC0, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x00},   /* U+001F */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0020 (space) */
  {0x18, 0x18, 0x18, 0x18, 0x18, 0x00, 0x18, 0x00},   /* U+0021 (!) */
  {0x66, 0x66, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0022 (") */
  {0x66, 0x66, 0x7E, 0x66, 0x7E, 0x66, 0x66, 0x00},   /* U+0023 (#) */
  {0x18, 0x3E, 0x60, 0x3C, 0x06, 0x7C, 0x18, 0x00},   /* U+0024 ($) */
  {0x00, 0xC6, 0xC6, 0x6C, 0x18, 0x36, 0x6C, 0x00},   /* U+0025 (%) */
  {0x3C, 0x66, 0x3C, 0x76, 0x66, 0x66, 0x3C, 0x00},   /* U+0026 (&) */
  {0x18, 0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0027 (') */
  {0x0C, 0x18, 0x30, 0x30, 0x30, 0x18, 0x0C, 0x00},   /* U+0028 (() */
  {0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x18, 0x30, 0x00},   /* U+0029 ()) */
  {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00},   /* U+002A (*) */
  {0x00, 0x18, 0x18, 0x7E, 0x18, 0x18, 0x00, 0x00},   /* U+002B (+) */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x0C},   /* U+002C (,) */
  {0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00},   /* U+002D (-) */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00},   /* U+002E (.) */
  {0x00, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x00},   /* U+002F (/) */
  {0x3C, 0x66, 0x6E, 0x76, 0x66, 0x66, 0x3C, 0x00},   /* U+0030 (0) */
  {0x18, 0x38, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00},   /* U+0031 (1) */
  {0x3C, 0x66, 0x06, 0x1C, 0x30, 0x66, 0x7E, 0x00},   /* U+0032 (2) */
  {0x3C, 0x66, 0x06, 0x3C, 0x06, 0x66, 0x3C, 0x00},   /* U+0033 (3) */
  {0x0E, 0x1E, 0x36, 0x66, 0x7E, 0x06, 0x0E, 0x00},   /* U+0034 (4) */
  {0x7E, 0x60, 0x7C, 0x06, 0x06, 0x66, 0x3C, 0x00},   /* U+0035 (5) */
  {0x3C, 0x60, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00},   /* U+0036 (6) */
  {0x7E, 0x66, 0x06, 0x0C, 0x18, 0x18, 0x18, 0x00},   /* U+0037 (7) */
  {0x3C, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00},   /* U+0038 (8) */
  {0x3C, 0x66, 0x66, 0x3E, 0x06, 0x0C, 0x38, 0x00},   /* U+0039 (9) */
  {0x00, 0x18, 0x18, 0x00, 0x00, 0x18, 0x18, 0x00},   /* U+003A (:) */
  {0x00, 0x18, 0x18, 0x00, 0x00, 0x18, 0x18, 0x0C},   /* U+003B (;) */
  {0x0C, 0x18, 0x30, 0x60, 0x30, 0x18, 0x0C, 0x00},   /* U+003C (<) */
  {0x00, 0x00, 0x7E, 0x00, 0x7E, 0x00, 0x00, 0x00},   /* U+003D (=) */
  {0x30, 0x18, 0x0C, 0x06, 0x0C, 0x18, 0x30, 0x00},   /* U+003E (>) */
  {0x3C, 0x66, 0x06, 0x0C, 0x18, 0x00, 0x18, 0x00},   /* U+003F (?) */
  {0x3C, 0x66, 0x66, 0x76, 0x7E, 0x76, 0x3B, 0x00},   /* U+0040 (@) */
  {0x18, 0x3C, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x00},   /* U+0041 (A) */
  {0x7C, 0x66, 0x66, 0x7C, 0x66, 0x66, 0x7C, 0x00},   /* U+0042 (B) */
  {0x3C, 0x66, 0x60, 0x60, 0x60, 0x66, 0x3C, 0x00},   /* U+0043 (C) */
  {0x78, 0x6C, 0x66, 0x66, 0x66, 0x6C, 0x78, 0x00},   /* U+0044 (D) */
  {0x7E, 0x60, 0x60, 0x7C, 0x60, 0x60, 0x7E, 0x00},   /* U+0045 (E) */
  {0x7E, 0x60, 0x60, 0x7C, 0x60, 0x60, 0x60, 0x00},   /* U+0046 (F) */
  {0x3C, 0x66, 0x60, 0x6E, 0x66, 0x66, 0x3C, 0x00},   /* U+0047 (G) */
  {0x66, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00},   /* U+0048 (H) */
  {0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00},   /* U+0049 (I) */
  {0x0E, 0x06, 0x06, 0x06, 0x06, 0x66, 0x3C, 0x00},   /* U+004A (J) */
  {0x66, 0x6C, 0x78, 0x70, 0x78, 0x6C, 0x66, 0x00},   /* U+004B (K) */
  {0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E, 0x00},   /* U+004C (L) */
  {0xC6, 0xEE, 0xFE, 0xD6, 0xC6, 0xC6, 0xC6, 0x00},   /* U+004D (M) */
  {0x66, 0xE6, 0xF6, 0xDE, 0xCE, 0x66, 0x66, 0x00},   /* U+004E (N) */
  {0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00},   /* U+004F (O) */
  {0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60, 0x60, 0x00},   /* U+0050 (P) */
  {0x3C, 0x66, 0x66, 0x66, 0x66, 0x6E, 0x3C, 0x0C},   /* U+0051 (Q) */
  {0x7C, 0x66, 0x66, 0x7C, 0x6C, 0x66, 0x66, 0x00},   /* U+0052 (R) */
  {0x3C, 0x66, 0x60, 0x3C, 0x06, 0x66, 0x3C, 0x00},   /* U+0053 (S) */
  {0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00},   /* U+0054 (T) */
  {0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x00},   /* U+0055 (U) */
  {0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00},   /* U+0056 (V) */
  {0xC6, 0xC6, 0xC6, 0xD6, 0xFE, 0xEE, 0xC6, 0x00},   /* U+0057 (W) */
  {0x66, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x66, 0x00},   /* U+0058 (X) */
  {0x66, 0x66, 0x66, 0x3C, 0x18, 0x18, 0x18, 0x00},   /* U+0059 (Y) */
  {0x7E, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x7E, 0x00},   /* U+005A (Z) */
  {0x7E, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E, 0x00},   /* U+005B ([) */
  {0xC0, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x00},   /* U+005C (\) */
  {0x7E, 0x06, 0x06, 0x06, 0x06, 0x06, 0x7E, 0x00},   /* U+005D (]) */
  {0x18, 0x3C, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+005E (^) */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF},   /* U+005F (_) */
  {0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0060 (`) */
  {0x00, 0x00, 0x3C, 0x06, 0x3E, 0x66, 0x3E, 0x00},   /* U+0061 (a) */
  {0x60, 0x60, 0x7C, 0x66, 0x66, 0x66, 0x7C, 0x00},   /* U+0062 (b) */
  {0x00, 0x00, 0x3C, 0x66, 0x60, 0x66, 0x3C, 0x00},   /* U+0063 (c) */
  {0x06, 0x06, 0x3E, 0x66, 0x66, 0x66, 0x3E, 0x00},   /* U+0064 (d) */
  {0x00, 0x00, 0x3C, 0x66, 0x7C, 0x60, 0x3C, 0x00},   /* U+0065 (e) */
  {0x1C, 0x36, 0x30, 0x7C, 0x30, 0x30, 0x30, 0x00},   /* U+0066 (f) */
  {0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x7C},   /* U+0067 (g) */
  {0x60, 0x60, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x00},   /* U+0068 (h) */
  {0x18, 0x00, 0x38, 0x18, 0x18, 0x18, 0x3C, 0x00},   /* U+0069 (i) */
  {0x0C, 0x00, 0x1C, 0x0C, 0x0C, 0x0C, 0xCC, 0x78},   /* U+006A (j) */
  {0x60, 0x60, 0x6C, 0x78, 0x70, 0x78, 0x6C, 0x00},   /* U+006B (k) */
  {0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00},   /* U+006C (l) */
  {0x00, 0x00, 0xCC, 0xFE, 0xD6, 0xD6, 0xC6, 0x00},   /* U+006D (m) */
  {0x00, 0x00, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x00},   /* U+006E (n) */
  {0x00, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3C, 0x00},   /* U+006F (o) */
  {0x00, 0x00, 0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60},   /* U+0070 (p) */
  {0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x0E},   /* U+0071 (q) */
  {0x00, 0x00, 0x7C, 0x66, 0x60, 0x60, 0x60, 0x00},   /* U+0072 (r) */
  {0x00, 0x00, 0x3E, 0x60, 0x3C, 0x06, 0x7C, 0x00},   /* U+0073 (s) */
  {0x30, 0x30, 0x7E, 0x30, 0x30, 0x36, 0x1C, 0x00},   /* U+0074 (t) */
  {0x00, 0x00, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x00},   /* U+0075 (u) */
  {0x00, 0x00, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00},   /* U+0076 (v) */
  {0x00, 0x00, 0xC6, 0xC6, 0xD6, 0xFE, 0x6C, 0x00},   /* U+0077 (w) */
  {0x00, 0x00, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x00},   /* U+0078 (x) */
  {0x00, 0x00, 0x66, 0x66, 0x66, 0x3E, 0x06, 0x7C},   /* U+0079 (y) */
  {0x00, 0x00, 0x7E, 0x0C, 0x18, 0x30, 0x7E, 0x00},   /* U+007A (z) */
  {0x0E, 0x18, 0x18, 0x70, 0x18, 0x18, 0x0E, 0x00},   /* U+007B ({) */
  {0x18, 0x18, 0x18, 0x00, 0x18, 0x18, 0x18, 0x00},   /* U+007C (|) */
  {0x70, 0x18, 0x18, 0x0E, 0x18, 0x18, 0x70, 0x00},   /* U+007D (}) */
  {0x76, 0xDC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+007E (~) */
  {0x1C, 0x36, 0x63, 0x6B, 0x6B, 0x03, 0x00, 0x00},   /* U+007F */
};

// =================================================================================
// 应用程序配置
// =================================================================================

#define LCD_WIDTH 800
#define LCD_HEIGHT 480
#define LCD_BPP 4
#define LCD_SIZE (LCD_WIDTH * LCD_HEIGHT * LCD_BPP)

#define TOUCH_DEBOUNCE_TIME 500000 

#define MAIN_MENU_BG_COLOR 0xADD8E6 // 淡蓝色

#define ICON_WIDTH 250
#define ICON_HEIGHT 160

#define THUMB_WIDTH 180
#define THUMB_HEIGHT 135
#define THUMBS_PER_PAGE 8
#define GRID_COLS 4
#define GRID_ROWS 2

#define SWIPE_THRESHOLD 150 // 滑动超过150像素才被识别
#define DOUBLE_CLICK_TIME 400000 // 双击时间阈值 (微秒)
#define LONG_PRESS_TIME_US 1000000L // 长按时间阈值 (1秒)
#define RECYCLE_BIN_PATH "/root/huishouzhan"
#define PHOTO_DIR_PATH "/root/bmpdk"

#define ANIMATION_STEPS 20 // 动画帧数
#define ANIMATION_DELAY_US 15000 // 每帧延迟(微秒)，约15ms

#define THUMBNAIL_CACHE_SIZE 16

typedef struct {
    int media_index;
    int *buffer;
} ThumbnailCacheEntry;

static ThumbnailCacheEntry g_thumbnail_cache[THUMBNAIL_CACHE_SIZE];
static int g_cache_count = 0; // 缩略图缓存计数器，用于FIFO替换策略

// 媒体文件类型枚举，用于区分不同类型的媒体文件
typedef enum {
    MEDIA_BMP,   // BMP格式图片文件
    MEDIA_AVI    // AVI格式视频文件
} MediaType;

// 应用程序状态枚举，定义程序的不同界面/功能状态
typedef enum {
    STATE_MAIN_MENU,               // 主菜单界面（程序启动默认界面）
    STATE_THUMBNAIL_VIEW,          // 缩略图浏览界面（展示所有媒体文件缩略图）
    STATE_PHOTO_ALBUM,             // 相册查看界面（全屏查看单张图片）
    STATE_SLIDESHOW,               // 幻灯片播放界面（自动轮播图片）
    STATE_RECYCLE_BIN_VIEW,        // 回收站浏览界面（展示已删除文件缩略图）
    STATE_RECYCLE_BIN_PHOTO_VIEW,  // 回收站图片查看界面（全屏查看回收站中的图片）
    STATE_STANDBY,                 // 待机界面（程序空闲时的简化界面）
    STATE_EXIT                     // 退出状态（标记程序结束）
} AppState;

// 动画类型枚举，定义界面切换时的过渡动画效果
typedef enum {
    ANIMATE_NONE,         // 无动画（直接切换）
    ANIMATE_SLIDE_LEFT,   // 向左滑动动画（新界面从右侧滑入）
    ANIMATE_SLIDE_RIGHT,  // 向右滑动动画（新界面从左侧滑入）
    ANIMATE_FADE          // 淡入淡出动画（未在代码中完全实现）
} AnimationType;

// =================================================================================
// 数据结构定义
// =================================================================================

// 媒体文件信息结构体，存储单个媒体文件的路径和类型
typedef struct {
    char *path;          // 文件的完整路径（如"/root/images/pic1.bmp"）
    MediaType type;      // 文件类型（MEDIA_BMP或MEDIA_AVI）
} MediaFile;

#pragma pack(1)  // 取消结构体对齐，按1字节对齐（确保与BMP文件格式一致）
// BMP文件头结构体，对应BMP文件的前14字节
typedef struct {
    unsigned char signature[2];  // BMP文件标识（固定为"BM"）
    unsigned int fileSize;       // 整个文件的大小（字节）
    unsigned short reserved1;    // 保留字段（未使用）
    unsigned short reserved2;    // 保留字段（未使用）
    unsigned int dataOffset;     // 像素数据在文件中的偏移量（字节）
} BMPFileHeader;

// BMP信息头结构体，存储图片的尺寸、格式等信息（共40字节）
typedef struct {
    unsigned int size;           // 信息头大小（固定为40）
    int width;                   // 图片宽度（像素）
    int height;                  // 图片高度（像素，负数表示像素数据从上到下存储）
    unsigned short planes;       // 色彩平面数（固定为1）
    unsigned short bitCount;     // 每个像素的位数（如24表示24位真彩色）
    unsigned int compression;    // 压缩方式（0表示不压缩）
    unsigned int imageSize;      // 像素数据大小（字节，不压缩时可设为0）
    int xPixelsPerMeter;         // 水平分辨率（像素/米）
    int yPixelsPerMeter;         // 垂直分辨率（像素/米）
    unsigned int colorsUsed;     // 使用的颜色数（0表示使用所有颜色）
    unsigned int colorsImportant;// 重要颜色数（0表示所有颜色都重要）
} BMPInfoHeader;
#pragma pack()  // 恢复默认结构体对齐

// 矩形区域结构体，用于表示屏幕上的一块区域（位置和大小）
typedef struct {
    int x, y;         // 矩形左上角坐标（x：水平坐标，y：垂直坐标）
    int width;        // 矩形宽度（像素）
    int height;       // 矩形高度（像素）
} Rect;

// =================================================================================
// 全局变量
// =================================================================================

static AppState g_app_state = STATE_MAIN_MENU;
static int g_lcd_fd = -1;
static int g_touch_fd = -1;
static int *g_screen_buffer = NULL;

static bool g_is_animating = false;

// 媒体文件相关
static MediaFile *g_media_files = NULL;
static int g_media_count = 0;
static int g_current_media_index = 0;

// 回收站相关
static MediaFile *g_recycle_bin_files = NULL;
static int g_recycle_bin_count = 0;
static int g_recycle_bin_start_index = 0;

// 缩略图相关
static int g_thumbnail_start_index = 0;
static Rect g_thumbnail_rects[THUMBS_PER_PAGE]; // 缩略图的位置和大小
static bool g_slideshow_is_paused = false;

// 按钮区域
static Rect g_rect_album;
static Rect g_rect_slideshow;
static Rect g_rect_exit;
static Rect g_rect_recycle_bin;
static Rect g_rect_return;
static Rect g_rect_voice_client_trigger; // 新增：语音识别按钮区域

// 缩略图缓存
static ThumbnailCacheEntry g_thumbnail_cache[THUMBNAIL_CACHE_SIZE];

static struct timeval g_touch_start_time;  // 触摸开始的时间（用于判断长按/滑动）

// =================================================================================
// 函数原型（声明后续实现的函数）
// =================================================================================
void play_video_with_mplayer(const char* video_path, AppState return_to_state);  // 调用mplayer播放视频
int get_bmp_dimensions(const char* bmp_path, int* width, int* height);          // 获取BMP图片的宽高
int* create_scaled_bmp_buffer(const char *bmp_path, int d_width, int d_height); // 创建缩放后的BMP像素缓冲区
int* get_or_create_thumbnail(int media_index);                                  // 获取或创建媒体文件的缩略图
int* get_or_create_thumbnail_for_recycle_bin(int media_index);                  // 获取或创建回收站文件的缩略图
void animate_transition(int *from_buffer, int *to_buffer, AnimationType type);  // 执行界面切换动画
void initialize_cache(void);                                                   // 初始化缩略图缓存
void cleanup_cache(void);                                                      // 清理缩略图缓存（释放内存）
int permanently_delete_media_file(int recycle_bin_index);                      // 永久删除回收站中的文件
int restore_media_file(int recycle_bin_index);                                 // 将回收站文件恢复到原始位置
void scan_directory(const char* dir);                                          // 扫描媒体目录，加载所有媒体文件
void scan_recycle_bin(void);                                                   // 扫描回收站目录，加载所有回收站文件
// =================================================================================
// 工具函数
// =================================================================================

bool is_touch_in_rect(int tx, int ty, const Rect* rect) {
    return (tx >= rect->x && tx <= (rect->x + rect->width) &&
            ty >= rect->y && ty <= (rect->y + rect->height));
}

int get_bmp_dimensions(const char* bmp_path, int* width, int* height) {
    FILE *bmp_file = fopen(bmp_path, "rb");
    if (bmp_file == NULL) {
        return -1;
    }

    BMPFileHeader file_header;
    if (fread(&file_header, sizeof(BMPFileHeader), 1, bmp_file) != 1) {
        fclose(bmp_file);
        return -1;
    }

    if (file_header.signature[0] != 'B' || file_header.signature[1] != 'M') {
        fclose(bmp_file);
        return -1;
    }

    BMPInfoHeader info_header;
    if (fread(&info_header, sizeof(BMPInfoHeader), 1, bmp_file) != 1) {
        fclose(bmp_file);
        return -1;
    }

    *width = info_header.width;
    *height = abs(info_header.height);

        fclose(bmp_file);
    return 0;
}

// =================================================================================
// 核心渲染函数 (内存优化版)
// =================================================================================

// 按比例缩小THUMB_WIDTH=180，THUMB_HEIGHT=135
int* create_scaled_bmp_buffer(const char *bmp_path, int d_width, int d_height) {
    FILE *bmp_file;
    BMPFileHeader file_header;
    BMPInfoHeader info_header;
    int s_width, s_height, row_size, image_size;
    unsigned char *bmp_data;
    int *thumb_buffer;
    int dest_y, dest_x;
    int src_x, src_y;
    int bmp_y_flipped, bmp_index;
    unsigned char b, g, r;
    
    bmp_file = fopen(bmp_path, "rb");
    if (!bmp_file) {
        perror("fopen in create_scaled_bmp_buffer");
        return NULL;
    }

    fread(&file_header, sizeof(BMPFileHeader), 1, bmp_file);
    fread(&info_header, sizeof(BMPInfoHeader), 1, bmp_file);

    if (file_header.signature[0] != 'B' || file_header.signature[1] != 'M' || 
        info_header.bitCount != 24 || info_header.compression != 0) {
        fclose(bmp_file);
        return NULL;
    }

    s_width = info_header.width;//原图像宽度
    s_height = abs(info_header.height);//原图像高度，取绝对值是因为 BMP 的height可能为负数（表示像素存储方向为 "从上到下"，正数则为 "从下到上"）
    
    row_size = ((s_width * info_header.bitCount + 31) / 32) * 4;
    image_size = row_size * s_height;
    
    bmp_data = (unsigned char *)malloc(image_size);
    if (!bmp_data) {
        fclose(bmp_file);
        return NULL;
    }

    fseek(bmp_file, file_header.dataOffset, SEEK_SET);
    fread(bmp_data, 1, image_size, bmp_file);
    fclose(bmp_file);

    thumb_buffer = (int*)malloc(d_width * d_height * sizeof(int));
    if (!thumb_buffer) {
        free(bmp_data);
        return NULL;
    }

    for (dest_y = 0; dest_y < d_height; dest_y++) {
        for (dest_x = 0; dest_x < d_width; dest_x++) {
            //最近邻插值法
            src_x = dest_x * s_width / d_width;// 原图像x坐标 = 目标x × 原宽 / 目标宽
            src_y = dest_y * s_height / d_height;// 原图像y坐标 = 目标y × 原高 / 目标高

            bmp_y_flipped = (info_header.height > 0) ? (s_height - 1 - src_y) : src_y;
            bmp_index = bmp_y_flipped * row_size + src_x * 3;//计算在原始图像的索引
            
            b = bmp_data[bmp_index];
            g = bmp_data[bmp_index + 1];
            r = bmp_data[bmp_index + 2];
            
            thumb_buffer[dest_y * d_width + dest_x] = (r << 16) | (g << 8) | b;
        }
    }

    free(bmp_data);
    return thumb_buffer;
}

// 缩略图缓存核心函数
int* get_or_create_thumbnail(int media_index) {
    int i;
    // 1. 检查缓存中是否已存在
    for (i = 0; i < THUMBNAIL_CACHE_SIZE; i++) {
        if (g_thumbnail_cache[i].buffer && g_thumbnail_cache[i].media_index == media_index) {
            // printf("[CACHE] Hit for index %d\n", media_index);
            return g_thumbnail_cache[i].buffer;
        }
    }

    // printf("[CACHE] Miss for index %d. Creating...\n", media_index);
    // 2. 缓存未命中，需要从文件创建
    const char* path = (g_media_files[media_index].type == MEDIA_BMP) ?
                       g_media_files[media_index].path : "./sp.bmp";

    int* new_thumb_buffer = create_scaled_bmp_buffer(path, THUMB_WIDTH, THUMB_HEIGHT);

    if (!new_thumb_buffer) {
        fprintf(stderr, "Failed to create thumbnail for %s\n", path);
        return NULL;
    }
    
    // 3. 将新创建的缩略图存入缓存
    // 决定要替换哪个缓存槽 (FIFO 简单策略)
    if (g_thumbnail_cache[g_cache_count].buffer) {
        free(g_thumbnail_cache[g_cache_count].buffer); // 释放旧的
    }
    g_thumbnail_cache[g_cache_count].media_index = media_index;
    g_thumbnail_cache[g_cache_count].buffer = new_thumb_buffer;
    
    // printf("[CACHE] Stored new thumbnail for index %d in slot %d\n", media_index, g_cache_count);

    g_cache_count = (g_cache_count + 1) % THUMBNAIL_CACHE_SIZE;

    return new_thumb_buffer;
}

// 新增：回收站的缩略图获取函数
int* get_or_create_thumbnail_for_recycle_bin(int media_index) {
    int i;
    // 1. 检查缓存中是否已存在 (注意: 这里的索引是回收站数组内的索引)
    for (i = 0; i < THUMBNAIL_CACHE_SIZE; i++) {
        // 为了区分, 可以在缓存中存一个负数索引
        if (g_thumbnail_cache[i].buffer && g_thumbnail_cache[i].media_index == -(media_index + 1)) {
            return g_thumbnail_cache[i].buffer;
        }
    }

    // 2. 缓存未命中，需要从文件创建
    const char* path = (g_recycle_bin_files[media_index].type == MEDIA_BMP) ?
                       g_recycle_bin_files[media_index].path : "./sp.bmp";

    int* new_thumb_buffer = create_scaled_bmp_buffer(path, THUMB_WIDTH, THUMB_HEIGHT);

    if (!new_thumb_buffer) {
        fprintf(stderr, "Failed to create thumbnail for %s\n", path);
        return NULL;
    }
    
    // 3. 将新创建的缩略图存入缓存
    if (g_thumbnail_cache[g_cache_count].buffer) {
        free(g_thumbnail_cache[g_cache_count].buffer);
    }
    g_thumbnail_cache[g_cache_count].media_index = -(media_index + 1); // 存为负数以区分
    g_thumbnail_cache[g_cache_count].buffer = new_thumb_buffer;
    
    g_cache_count = (g_cache_count + 1) % THUMBNAIL_CACHE_SIZE;

    return new_thumb_buffer;
}

// 新增：绘制单个8x8字符
void draw_char_at(int* buffer, int x, int y, char c, int color) {
    int i, j;
    int font_width = 8;
    int font_height = 8;
    const unsigned char* glyph;

    // 基本ASCII码检查，防止数组越界
    if (c < 0 || c > 127) return;

    glyph = font8x8_basic[(int)c];

    for (i = 0; i < font_height; i++) {
        for (j = 0; j < font_width; j++) {
            // 检查字体数据的每一位，如果为1则绘制像素
            if ((glyph[i] >> (7 - j)) & 1) {
                int screen_x = x + j;
                int screen_y = y + i;
                if (screen_x >= 0 && screen_x < LCD_WIDTH && screen_y >= 0 && screen_y < LCD_HEIGHT) {
                    buffer[screen_y * LCD_WIDTH + screen_x] = color;
                }
            }
        }
    }
}

// 新增：在指定矩形容器内居中绘制文本
void draw_text_centered(int* buffer, const char* text, Rect container, int y_offset, int color) {
    int len, max_chars, chars_to_draw, text_width, start_x, start_y, i;
    int font_width = 8;

    len = strlen(text);
    max_chars = container.width / font_width;
    
    // 如果文件名太长，则截断
    chars_to_draw = (len > max_chars) ? max_chars : len;

    text_width = chars_to_draw * font_width;
    start_x = container.x + (container.width - text_width) / 2;
    start_y = container.y + y_offset;

    for (i = 0; i < chars_to_draw; i++) {
        draw_char_at(buffer, start_x + i * font_width, start_y, text[i], color);
    }
}
//？？？无
int draw_bmp_scaled_to_buffer(const char *bmp_path, int* buffer, int dx, int dy, int d_width, int d_height) {
    FILE *bmp_file = fopen(bmp_path, "rb");
    BMPFileHeader file_header;
    BMPInfoHeader info_header;
    int s_width, s_height, row_size, image_size;
    unsigned char *bmp_data;
    int dest_y, dest_x;
    int src_x, src_y;
    int screen_x, screen_y;
    int bmp_y_flipped, bmp_index;
    unsigned char b, g, r;

    if (bmp_file == NULL) return -1;

    fread(&file_header, sizeof(BMPFileHeader), 1, bmp_file);
    fread(&info_header, sizeof(BMPInfoHeader), 1, bmp_file);

    if (file_header.signature[0] != 'B' || file_header.signature[1] != 'M' || 
        info_header.bitCount != 24 || info_header.compression != 0) {
        fclose(bmp_file);
        return -1;
    }

    s_width = info_header.width;
    s_height = abs(info_header.height);
    row_size = ((s_width * info_header.bitCount + 31) / 32) * 4;
    image_size = row_size * s_height;
    
    bmp_data = (unsigned char *)malloc(image_size);
    if (bmp_data == NULL) {
        fclose(bmp_file);
        return -1;
    }

    fseek(bmp_file, file_header.dataOffset, SEEK_SET);
    fread(bmp_data, 1, image_size, bmp_file);
    fclose(bmp_file);

    for (dest_y = 0; dest_y < d_height; dest_y++) {
        for (dest_x = 0; dest_x < d_width; dest_x++) {
            src_x = dest_x * s_width / d_width;
            src_y = dest_y * s_height / d_height;

            screen_x = dx + dest_x;
            screen_y = dy + dest_y;

            if (screen_x < 0 || screen_x >= LCD_WIDTH || screen_y < 0 || screen_y >= LCD_HEIGHT) continue;

            bmp_y_flipped = (info_header.height > 0) ? (s_height - 1 - src_y) : src_y;
            bmp_index = bmp_y_flipped * row_size + src_x * 3;
            b = bmp_data[bmp_index];
            g = bmp_data[bmp_index + 1];
            r = bmp_data[bmp_index + 2];
            
            buffer[screen_y * LCD_WIDTH + screen_x] = (r << 16) | (g << 8) | b;
        }
    }

        free(bmp_data);
    return 0;
}
//显示bmp图片
int draw_bmp_to_buffer(const char *bmp_path, int* buffer, int dx, int dy, bool use_alpha) {
    FILE *bmp_file = fopen(bmp_path, "rb");//二进制打开
    BMPFileHeader file_header;
    BMPInfoHeader info_header;
    int width, height, row_size, image_size;
    unsigned char *bmp_data;
    int y, x;
    int screen_x, screen_y, bmp_y, bmp_index;
    unsigned char b, g, r;

    if (bmp_file == NULL) return -1;

    fread(&file_header, sizeof(BMPFileHeader), 1, bmp_file);
    fread(&info_header, sizeof(BMPInfoHeader), 1, bmp_file);

    if (file_header.signature[0] != 'B' || file_header.signature[1] != 'M' || 
        info_header.bitCount != 24 || info_header.compression != 0) {
        fclose(bmp_file);
        return -1;
    }

    width = info_header.width;
    height = abs(info_header.height);
    row_size = ((width * info_header.bitCount + 31) / 32) * 4;
    image_size = row_size * height;
    
    bmp_data = (unsigned char *)malloc(image_size);
    if (bmp_data == NULL) {
        fclose(bmp_file);
        return -1;
    }

    fseek(bmp_file, file_header.dataOffset, SEEK_SET);
    fread(bmp_data, 1, image_size, bmp_file);
    fclose(bmp_file);
    
    for (y = 0; y < height; y++) {
        for (x = 0; x < width; x++) {
            screen_x = dx + x;
            screen_y = dy + y;

            if (screen_x < 0 || screen_x >= LCD_WIDTH || screen_y < 0 || screen_y >= LCD_HEIGHT) {
                continue;
            }

            bmp_y = (info_header.height > 0) ? (height - 1 - y) : y;
            bmp_index = bmp_y * row_size + x * 3;
            b = bmp_data[bmp_index];
            g = bmp_data[bmp_index + 1];
            r = bmp_data[bmp_index + 2];
            
            // 如果启用了Alpha（透明），并且颜色是纯黑，则跳过该像素
            if (use_alpha && r == 0 && g == 0 && b == 0) {
                continue;
            }

            buffer[screen_y * LCD_WIDTH + screen_x] = (r << 16) | (g << 8) | b;
        }
    }

    free(bmp_data);
    return 0;
}

// 新增：用于视觉调试，在指定坐标绘制一个方块
void draw_debug_square(int* buffer, int x, int y, int size, int color) {
    int i, j;
    int start_x = x - size / 2;
    int start_y = y - size / 2;

    for (i = start_y; i < start_y + size; i++) {
        for (j = start_x; j < start_x + size; j++) {
            if (i >= 0 && i < LCD_HEIGHT && j >= 0 && j < LCD_WIDTH) {
                 buffer[i * LCD_WIDTH + j] = color;
            }
        }
    }
}

// =================================================================================
// 界面渲染
// =================================================================================
//主界面
void render_main_menu() {
    int i;
    int spacing;
    Rect title_container;

    for (i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = MAIN_MENU_BG_COLOR;

    // 绘制标题
    title_container = (Rect){0, 30, LCD_WIDTH, 10};
    draw_text_centered(g_screen_buffer, "GEC6818 Media Center", title_container, 0, 0x000000); // 黑色标题

    // 绘制图标
    spacing = (LCD_WIDTH - ICON_WIDTH * 3) / 4;
    g_rect_album = (Rect){spacing, (LCD_HEIGHT - ICON_HEIGHT) / 2, ICON_WIDTH, ICON_HEIGHT};
    g_rect_slideshow = (Rect){g_rect_album.x + ICON_WIDTH + spacing, (LCD_HEIGHT - ICON_HEIGHT) / 2, ICON_WIDTH, ICON_HEIGHT};
    g_rect_exit = (Rect){g_rect_slideshow.x + ICON_WIDTH + spacing, (LCD_HEIGHT - ICON_HEIGHT) / 2, ICON_WIDTH, ICON_HEIGHT};

    draw_bmp_to_buffer("./xiangce.bmp", g_screen_buffer, g_rect_album.x, g_rect_album.y, true);
    draw_bmp_to_buffer("./huandpian.bmp", g_screen_buffer, g_rect_slideshow.x, g_rect_slideshow.y, true);
    draw_bmp_to_buffer("./tuichu.bmp", g_screen_buffer, g_rect_exit.x, g_rect_exit.y, true);

    // 绘制图标下方的文本
    draw_text_centered(g_screen_buffer, "Album", g_rect_album, ICON_HEIGHT + 10, 0x000000);
    draw_text_centered(g_screen_buffer, "Slideshow", g_rect_slideshow, ICON_HEIGHT + 10, 0x000000);
    draw_text_centered(g_screen_buffer, "Exit", g_rect_exit, ICON_HEIGHT + 10, 0x000000);

    // 新增: 绘制回收站图标
    int btn_w, btn_h;
    if (get_bmp_dimensions("./huishouzhan.bmp", &btn_w, &btn_h) == 0) {
        int padding = 10;
        g_rect_recycle_bin.width = btn_w + 2 * padding;
        g_rect_recycle_bin.height = btn_h + 2 * padding;
        g_rect_recycle_bin.x = LCD_WIDTH - g_rect_recycle_bin.width - padding;
        g_rect_recycle_bin.y = LCD_HEIGHT - g_rect_recycle_bin.height - padding;

        int draw_x = g_rect_recycle_bin.x + padding;
        int draw_y = g_rect_recycle_bin.y + padding;
        draw_bmp_to_buffer("./huishouzhan.bmp", g_screen_buffer, draw_x, draw_y, true);
    }
}
//缩略图排版
void render_thumbnail_view() {
    int i;
    int h_spacing;
    int v_spacing;
    int start_index;
    int current_index;
    int row, col;
    int x, y;
    int btn_w, btn_h;
    const char *full_path;
    const char *filename;
    Rect text_container;
    bool circular = g_media_count > THUMBS_PER_PAGE;

    for(i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x1A1A1A; // 深灰色背景

    h_spacing = (LCD_WIDTH - GRID_COLS * THUMB_WIDTH) / (GRID_COLS + 1);// 水平间距 = (LCD宽 - 列数*缩略图宽) / (列数 + 1)
    v_spacing = (LCD_HEIGHT - GRID_ROWS * THUMB_HEIGHT) / (GRID_ROWS + 1);// 垂直间距 = (LCD高 - 行数*缩略图高) / (行数 + 1)
    
    start_index = g_thumbnail_start_index;
    for (i = 0; i < THUMBS_PER_PAGE; i++) {
        // 根据是否启用循环滚动来计算索引
        if (circular) {
            current_index = (start_index + i) % g_media_count;
        } else {
            current_index = start_index + i;
        }

        if (current_index >= g_media_count) {
            // 将未使用的缩略图区域标记为无效
            g_thumbnail_rects[i] = (Rect){-1, -1, 0, 0};
            continue;
        };

        row = i / GRID_COLS;//计算行索引
        col = i % GRID_COLS;//列索引

        x = h_spacing + col * (THUMB_WIDTH + h_spacing);
        y = v_spacing + row * (THUMB_HEIGHT + v_spacing);

        g_thumbnail_rects[i] = (Rect){x, y, THUMB_WIDTH, THUMB_HEIGHT};
        
        int *thumb_buffer = get_or_create_thumbnail(current_index);//获取缩略图
        if (thumb_buffer) {
            int k;
            for (k = 0; k < THUMB_WIDTH * THUMB_HEIGHT; k++) {
                 int dest_x = g_thumbnail_rects[i].x + (k % THUMB_WIDTH);//求解所在列
                 int dest_y = g_thumbnail_rects[i].y + (k / THUMB_WIDTH);//所在行
                 if (dest_y < LCD_HEIGHT && dest_x < LCD_WIDTH) {
                    g_screen_buffer[dest_y * LCD_WIDTH + dest_x] = thumb_buffer[k];
                 }
            }
        }

        // 统一为所有媒体文件绘制文件名
        full_path = g_media_files[current_index].path;//获取路径
        filename = strrchr(full_path, '/');
        if (filename == NULL) {
            filename = full_path;
        } else {
            filename++;// 跳过斜杠，指向文件名的起始位置
        }
        text_container = g_thumbnail_rects[i];
        draw_text_centered(g_screen_buffer, filename, text_container, THUMB_HEIGHT + 5, 0xFFFFFF);
    }
    
    // --- 渲染导航按钮 ---
    // 返回按钮 (右下角)
    if (get_bmp_dimensions("./fanhui.bmp", &btn_w, &btn_h) == 0) {
        int padding = 20; // 为触摸区域增加的额外边距
        // g_rect_return 现在代表整个可点击区域, 紧贴右下角
        g_rect_return.width = btn_w + 2 * padding;
        g_rect_return.height = btn_h + 2 * padding;
        g_rect_return.x = LCD_WIDTH - g_rect_return.width;
        g_rect_return.y = LCD_HEIGHT - g_rect_return.height;

        // 计算图标的实际绘制位置，使其在触摸区域内居中
        int draw_x = g_rect_return.x + padding;
        int draw_y = g_rect_return.y + padding;
        draw_bmp_to_buffer("./fanhui.bmp", g_screen_buffer, draw_x, draw_y, true);
    }
}
//幻灯片排版
void render_slideshow() {
    // 幻灯片跳过视频
    while(g_media_files[g_current_media_index].type == MEDIA_AVI) {
        g_current_media_index = (g_current_media_index + 1) % g_media_count;
    }

    // 绘制当前图片
    if (g_media_count > 0) {
        // 全屏绘制，不使用透明贴图，背景就是图片本身
        draw_bmp_to_buffer(g_media_files[g_current_media_index].path, g_screen_buffer, 0, 0, false);
    }
    
    if (g_slideshow_is_paused) {
        draw_char_at(g_screen_buffer, LCD_WIDTH - 30, 10, '|', 0xFFFFFF);
        draw_char_at(g_screen_buffer, LCD_WIDTH - 20, 10, '|', 0xFFFFFF);
    }
    // 注意: 此处已根据要求，移除了返回按钮的绘制逻辑。
    // 现在退出幻灯片需要点击屏幕中央区域。
}

void render_photo_album() {
    draw_bmp_to_buffer(g_media_files[g_current_media_index].path, g_screen_buffer, 0, 0, false);
}
//回收站缩略图显示
void render_recycle_bin_view() {
    int i;
    int h_spacing, v_spacing;
    int start_index, current_index;
    int row, col;
    int x, y;
    int btn_w, btn_h;
    const char *full_path, *filename;
    Rect text_container;
    bool circular = g_recycle_bin_count > THUMBS_PER_PAGE;

    for(i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x333333; // 回收站用更深的灰色

    h_spacing = (LCD_WIDTH - GRID_COLS * THUMB_WIDTH) / (GRID_COLS + 1);
    v_spacing = (LCD_HEIGHT - GRID_ROWS * THUMB_HEIGHT) / (GRID_ROWS + 1);
    
    start_index = g_recycle_bin_start_index;
    for (i = 0; i < THUMBS_PER_PAGE; i++) {
        if (circular) {
            current_index = (start_index + i) % g_recycle_bin_count;
        } else {
            current_index = start_index + i;
        }

        if (current_index >= g_recycle_bin_count) {
            g_thumbnail_rects[i] = (Rect){-1, -1, 0, 0};
            continue;
        };

        row = i / GRID_COLS;
        col = i % GRID_COLS;

        x = h_spacing + col * (THUMB_WIDTH + h_spacing);
        y = v_spacing + row * (THUMB_HEIGHT + v_spacing);

        g_thumbnail_rects[i] = (Rect){x, y, THUMB_WIDTH, THUMB_HEIGHT};
        
        // 注意: 调用回收站专用的缩略图函数
        int *thumb_buffer = get_or_create_thumbnail_for_recycle_bin(current_index);
        if (thumb_buffer) {
            int k;
            for (k = 0; k < THUMB_WIDTH * THUMB_HEIGHT; k++) {
                 int dest_x = g_thumbnail_rects[i].x + (k % THUMB_WIDTH);
                 int dest_y = g_thumbnail_rects[i].y + (k / THUMB_WIDTH);
                 if (dest_y < LCD_HEIGHT && dest_x < LCD_WIDTH) {
                    g_screen_buffer[dest_y * LCD_WIDTH + dest_x] = thumb_buffer[k];
                 }
            }
        }

        full_path = g_recycle_bin_files[current_index].path;
        filename = strrchr(full_path, '/');
        filename = (filename == NULL) ? full_path : filename + 1;
        text_container = g_thumbnail_rects[i];
        draw_text_centered(g_screen_buffer, filename, text_container, THUMB_HEIGHT + 5, 0xAAAAAA); // 文件名为灰色
    }
    
    // 渲染返回按钮
    if (get_bmp_dimensions("./fanhui.bmp", &btn_w, &btn_h) == 0) {
        int padding = 20;
        g_rect_return.width = btn_w + 2 * padding;
        g_rect_return.height = btn_h + 2 * padding;
        g_rect_return.x = LCD_WIDTH - g_rect_return.width;
        g_rect_return.y = LCD_HEIGHT - g_rect_return.height;
        int draw_x = g_rect_return.x + padding;
        int draw_y = g_rect_return.y + padding;
        draw_bmp_to_buffer("./fanhui.bmp", g_screen_buffer, draw_x, draw_y, true);
    }
}

void render_recycle_bin_photo_view() {
    // 确保索引有效
    if (g_current_media_index >= 0 && g_current_media_index < g_recycle_bin_count) {
        draw_bmp_to_buffer(g_recycle_bin_files[g_current_media_index].path, g_screen_buffer, 0, 0, false);
    } else {
        // 如果索引无效或回收站为空，显示一个提示信息
        int i;
        for (i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x1A1A1A;
        Rect text_container = {0, 0, LCD_WIDTH, LCD_HEIGHT};
        draw_text_centered(g_screen_buffer, "No image to display", text_container, 0, 0xFFFFFF);
    }
}

void display_on_lcd() {
    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) return;
    memcpy(fbp, g_screen_buffer, LCD_SIZE);
    munmap(fbp, LCD_SIZE);
}


// =================================================================================
// 事件处理
// =================================================================================

void transform_touch_coords(int *raw_x, int *raw_y) {
    // 根据您的触摸屏进行校准
    // 大部分 GEC6818 配套的触摸屏是 1024x600 的分辨率
    // 如果您的触摸屏X轴和Y轴是反的，或者方向是反的，可以在这里调整
    
    *raw_x = *raw_x * LCD_WIDTH / 800;
    *raw_y = *raw_y * LCD_HEIGHT / 480;
}
//主界面事件处理
void handle_touch_main_menu(int x, int y) {
    // 检查是否点击了语音识别按钮
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 不需要等待子进程，让它在后台运行
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", pid);
        }
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_album)) {
        printf("[STATE] Action: Main Menu -> Thumbnail View\n");
        g_thumbnail_start_index = 0; // 每次进入都从第一张图开始

        int* next_screen_buffer = malloc(LCD_SIZE);
        if(!next_screen_buffer) {
            g_app_state = STATE_THUMBNAIL_VIEW;//到缩略图
            return;
        }
        
        int* temp_ptr = g_screen_buffer;
        g_screen_buffer = next_screen_buffer;
        render_thumbnail_view();
        g_screen_buffer = temp_ptr;

        animate_transition(g_screen_buffer, next_screen_buffer, ANIMATE_SLIDE_LEFT);
        free(next_screen_buffer);
        g_app_state = STATE_THUMBNAIL_VIEW;

    } else if (is_touch_in_rect(x, y, &g_rect_slideshow)) {
        if (g_media_count > 0) {
            printf("[STATE] Action: Main Menu -> Slideshow\n");
            g_current_media_index = 0; // 每次都从第一张图开始
            
            int* next_screen_buffer = malloc(LCD_SIZE);
            if(!next_screen_buffer) {
                g_app_state = STATE_SLIDESHOW;
                return;
            }

            int* temp_ptr = g_screen_buffer;
            g_screen_buffer = next_screen_buffer;
            render_slideshow();
            g_screen_buffer = temp_ptr;
            
            animate_transition(g_screen_buffer, next_screen_buffer, ANIMATE_SLIDE_LEFT);
            free(next_screen_buffer);
            g_app_state = STATE_SLIDESHOW;

    } else {
            fprintf(stderr, "警告: 相册中没有图片, 无法启动幻灯片。\n");
        }
    } else if (is_touch_in_rect(x, y, &g_rect_exit)) {
        printf("[STATE] Action: -> EXIT\n");
        g_app_state = STATE_EXIT;
    } else if (is_touch_in_rect(x, y, &g_rect_recycle_bin)) {
        printf("[STATE] Action: Main Menu -> Recycle Bin\n");
        g_recycle_bin_start_index = 0;
        
        // 进入回收站前清理缓存
        cleanup_cache();
        initialize_cache();
        
        g_app_state = STATE_RECYCLE_BIN_VIEW;
    }
}
//缩略图界面事件处理
void handle_touch_thumbnail_view(int x, int y) {
    int i;
    int start_index;
    int current_media_index;
    bool circular = g_media_count > THUMBS_PER_PAGE;

    // 检查是否点击返回按钮
    if (is_touch_in_rect(x, y, &g_rect_return)) {
        printf("[STATE] Action: Thumbnail View -> Main Menu\n");
        
        int* next_screen_buffer = malloc(LCD_SIZE);
        if(!next_screen_buffer) {
            g_app_state = STATE_MAIN_MENU;
            return;
        }

        int* temp_ptr = g_screen_buffer;
        g_screen_buffer = next_screen_buffer;
        render_main_menu();
        g_screen_buffer = temp_ptr;

        animate_transition(g_screen_buffer, next_screen_buffer, ANIMATE_SLIDE_RIGHT);
        free(next_screen_buffer);
        g_app_state = STATE_MAIN_MENU;
        return;
    }

    // 检查是否点击了某个缩略图
    start_index = g_thumbnail_start_index;
    for (i = 0; i < THUMBS_PER_PAGE; i++) {
        if (g_thumbnail_rects[i].width <= 0) continue; // 跳过无效区域
        
        if (is_touch_in_rect(x, y, &g_thumbnail_rects[i])) {
            // 根据是否循环来计算真实索引
            if (circular) {
                 current_media_index = (start_index + i) % g_media_count;
        } else {
                 current_media_index = start_index + i;
            }
            
            if (current_media_index >= g_media_count) continue; // 再次检查，确保安全

            printf("[DEBUG] Thumbnail Click: Touched thumb #%d, resolves to media index %d ('%s')\n", i, current_media_index, g_media_files[current_media_index].path);
            g_current_media_index = current_media_index;
            if (g_media_files[g_current_media_index].type == MEDIA_BMP) {
                printf("[STATE] Action: Thumbnail View -> Photo Album\n");

                int* next_screen_buffer = malloc(LCD_SIZE);
                if(!next_screen_buffer) {
                    g_app_state = STATE_PHOTO_ALBUM;
                    return;
                }

                int* temp_ptr = g_screen_buffer;
                g_screen_buffer = next_screen_buffer;
                render_photo_album();
                g_screen_buffer = temp_ptr;

                // 暂用滑动效果，后续可以改为淡入淡出或缩放
                animate_transition(g_screen_buffer, next_screen_buffer, ANIMATE_SLIDE_LEFT);
                free(next_screen_buffer);
                g_app_state = STATE_PHOTO_ALBUM; 

            } else { // MEDIA_AVI
                printf("[ACTION] Calling video player...\n");
                play_video_with_mplayer(g_media_files[g_current_media_index].path, STATE_THUMBNAIL_VIEW);
                printf("[ACTION] Video player returned. Redrawing thumbnail view.\n");
            }
            return;
        }
    }
}
//相册展开界面事件处理
void handle_touch_photo_album(int x, int y) {
    int quarter_width = LCD_WIDTH / 4;
    int next_index = g_current_media_index;
    AnimationType anim_type = ANIMATE_NONE;

    if (x < quarter_width) {
        // 左侧 1/4: 上一张
        next_index = (g_current_media_index - 1 + g_media_count) % g_media_count;
        anim_type = ANIMATE_SLIDE_RIGHT;
        printf("[DEBUG] Photo Album Click: Left side. New index: %d\n", next_index);
    } else if (x > quarter_width * 3) {
        // 右侧 1/4: 下一张
        next_index = (g_current_media_index + 1) % g_media_count;
        anim_type = ANIMATE_SLIDE_LEFT;
        printf("[DEBUG] Photo Album Click: Right side. New index: %d\n", next_index);
    } else {
        // 中间 1/2: 返回缩略图界面
        printf("[STATE] Action: Photo Album -> Thumbnail View\n");
        
        int* next_screen_buffer = malloc(LCD_SIZE);
        if(!next_screen_buffer) {
            g_app_state = STATE_THUMBNAIL_VIEW;
            return;
        }

        int* temp_ptr = g_screen_buffer;
        g_screen_buffer = next_screen_buffer;
        render_thumbnail_view();
        g_screen_buffer = temp_ptr;
        
        animate_transition(g_screen_buffer, next_screen_buffer, ANIMATE_SLIDE_RIGHT);
        free(next_screen_buffer);
        g_app_state = STATE_THUMBNAIL_VIEW;
        return;
    }

    if (anim_type != ANIMATE_NONE) {
        // 动画切换图片
        int* next_image_buffer = malloc(LCD_SIZE);
        if (!next_image_buffer) {
             g_current_media_index = next_index; // 无动画回退
             return;
        }

        if (draw_bmp_to_buffer(g_media_files[next_index].path, next_image_buffer, 0, 0, false) != 0) {
             free(next_image_buffer);
             g_current_media_index = next_index; // 加载失败，但仍然切换索引
             return;
        }

        animate_transition(g_screen_buffer, next_image_buffer, anim_type);

        free(next_image_buffer);
        g_current_media_index = next_index;
    }
}
//回收站缩略图事件
void handle_touch_recycle_bin_view(int x, int y) {
    int i;
    // 检查是否点击返回按钮
    if (is_touch_in_rect(x, y, &g_rect_return)) {
        printf("[STATE] Action: Recycle Bin -> Main Menu\n");
        // 返回主菜单前清理缓存
        cleanup_cache();
        initialize_cache();
        g_app_state = STATE_MAIN_MENU;
        return;
    }

    // 点击缩略图进入大图查看
    for (i = 0; i < THUMBS_PER_PAGE; i++) {
        if (g_thumbnail_rects[i].width > 0 && is_touch_in_rect(x, y, &g_thumbnail_rects[i])) {
            bool circular = g_recycle_bin_count > THUMBS_PER_PAGE;
            int media_idx;
            if (circular) {
                 media_idx = (g_recycle_bin_start_index + i) % g_recycle_bin_count;
            } else {
                 media_idx = g_recycle_bin_start_index + i;
            }
            if (media_idx >= g_recycle_bin_count) continue;

            // 只处理图片点击
            if (g_recycle_bin_files[media_idx].type == MEDIA_BMP) {
                printf("[STATE] Action: Recycle Bin View -> Recycle Bin Photo View\n");
                g_current_media_index = media_idx;
                g_app_state = STATE_RECYCLE_BIN_PHOTO_VIEW;
            } else {
                printf("[ACTION] Recycle bin video playback not implemented.\n");
            }
            return;
        }
    }
}
//回收站展开图片事件
void handle_touch_recycle_bin_photo_view(int x, int y) {
    if (g_recycle_bin_count == 0) { // 如果回收站为空，任何点击都返回
        g_app_state = STATE_RECYCLE_BIN_VIEW;
        return;
    }

    int quarter_width = LCD_WIDTH / 4;
    int next_index = g_current_media_index;
    AnimationType anim_type = ANIMATE_NONE;

    if (x < quarter_width) {
        // 左侧 1/4: 上一张 (循环查找上一个BMP)
        int original_index = g_current_media_index;
        do {
            next_index = (next_index - 1 + g_recycle_bin_count) % g_recycle_bin_count;
        } while (g_recycle_bin_files[next_index].type != MEDIA_BMP && next_index != original_index);
        
        if (next_index != g_current_media_index) anim_type = ANIMATE_SLIDE_RIGHT;
        printf("[DEBUG] Recycle Photo View Click: Left. New index: %d\n", next_index);

    } else if (x > quarter_width * 3) {
        // 右侧 1/4: 下一张 (循环查找下一个BMP)
        int original_index = g_current_media_index;
        do {
            next_index = (next_index + 1) % g_recycle_bin_count;
        } while (g_recycle_bin_files[next_index].type != MEDIA_BMP && next_index != original_index);
        
        if (next_index != g_current_media_index) anim_type = ANIMATE_SLIDE_LEFT;
        printf("[DEBUG] Recycle Photo View Click: Right. New index: %d\n", next_index);

    } else {
        // 中间 1/2: 返回回收站缩略图界面
        printf("[STATE] Action: Recycle Photo View -> Recycle Bin View\n");
        g_app_state = STATE_RECYCLE_BIN_VIEW;
        return;
    }

    if (anim_type != ANIMATE_NONE) {
        int* next_image_buffer = malloc(LCD_SIZE);
        if (!next_image_buffer) {
             g_current_media_index = next_index;
             return;
        }

        if (draw_bmp_to_buffer(g_recycle_bin_files[next_index].path, next_image_buffer, 0, 0, false) == 0) {
            animate_transition(g_screen_buffer, next_image_buffer, anim_type);
        }

        free(next_image_buffer);
        g_current_media_index = next_index;
    }
}

// =================================================================================
// 初始化与清理
// =================================================================================

MediaFile *get_media_files_from_dir(const char *dir_path, int *count) {
    DIR *dir = opendir(dir_path);
    if (!dir) return NULL;
    int capacity = 10;
    MediaFile *files = (MediaFile *)malloc(capacity * sizeof(MediaFile));
        *count = 0;
    struct dirent *entry;
    while ((entry = readdir(dir)) != NULL) {
        const char *name = entry->d_name;
        size_t len = strlen(name);
        bool is_bmp = (len > 4 && strcasecmp(name + len - 4, ".bmp") == 0);//// 判断是否为.bmp文件（忽略大小写，如.BMP也会被识别）
        bool is_avi = (len > 4 && strcasecmp(name + len - 4, ".avi") == 0);

        if (is_bmp || is_avi) {
            if (*count >= capacity) {
                capacity *= 2;
                files = (MediaFile *)realloc(files, capacity * sizeof(MediaFile));
            }
            char* path = (char*)malloc(strlen(dir_path) + len + 2);
            sprintf(path, "%s/%s", dir_path, name);
            files[*count].path = path;
            files[*count].type = is_bmp ? MEDIA_BMP : MEDIA_AVI;
            (*count)++;
        }
    }
                    closedir(dir);
    return files;
}

void free_media_files() {
    int i;
    for (i = 0; i < g_media_count; i++) free(g_media_files[i].path);
    free(g_media_files);
}

void free_recycle_bin_files() {
    int i;
    for (i = 0; i < g_recycle_bin_count; i++) free(g_recycle_bin_files[i].path);
    free(g_recycle_bin_files);
}

void initialize_cache() {
    int i;
    for (i = 0; i < THUMBNAIL_CACHE_SIZE; i++) {
        g_thumbnail_cache[i].media_index = -1;
        g_thumbnail_cache[i].buffer = NULL;
    }
}

void cleanup_cache() {
    int i;
    printf("Cleaning up thumbnail cache...\n");
    for (i = 0; i < THUMBNAIL_CACHE_SIZE; i++) {
        if (g_thumbnail_cache[i].buffer) {
            free(g_thumbnail_cache[i].buffer);
        }
    }
}

int initialize_app(const char* dir) {
    // 打开LCD设备
    g_lcd_fd = open("/dev/fb0", O_RDWR);
    if (g_lcd_fd == -1) {
        perror("无法打开 /dev/fb0");
        return -1;
    }

    // 打开触摸设备
    g_touch_fd = open("/dev/input/event0", O_RDONLY);
    if (g_touch_fd == -1) {
        perror("无法打开 /dev/input/event0");
        close(g_lcd_fd);
        return -1;
    }
    
    // 分配屏幕缓冲区
    g_screen_buffer = (int*)malloc(LCD_SIZE);
    if (!g_screen_buffer) {
        perror("内存分配失败");
        close(g_touch_fd);
        close(g_lcd_fd);
        return -1;
    }

    // 初始化按钮区域
    g_rect_album = (Rect){100, 100, 200, 100};
    g_rect_slideshow = (Rect){100, 250, 200, 100};
    g_rect_recycle_bin = (Rect){400, 100, 200, 100};
    g_rect_exit = (Rect){400, 250, 200, 100};
    g_rect_return = (Rect){LCD_WIDTH - 64, LCD_HEIGHT - 64, 64, 64};
    
    // 初始化语音识别按钮区域（右上角）
    g_rect_voice_client_trigger = (Rect){LCD_WIDTH - 60, 0, 60, 60};

    // 初始化缩略图缓存
    initialize_cache();

    // 扫描图片目录
    scan_directory(dir);
    scan_recycle_bin();

    return 0;
}

void cleanup_app() {
    cleanup_cache();
    if(g_media_files) free_media_files();
    if(g_recycle_bin_files) free_recycle_bin_files();
    free(g_screen_buffer);
    if(g_lcd_fd != -1) close(g_lcd_fd);
    if(g_touch_fd != -1) close(g_touch_fd);
    printf("应用程序已清理并退出。\n");
}

// =================================================================================
// 主函数
// =================================================================================
int main(int argc, char *argv[]) {
    printf("应用程序 V22 - 统一事件循环版 启动...\n");
    
    // 检查命令行参数
    bool start_in_thumbnail = false;
    bool start_in_slideshow = false;
    
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--thumbnail") == 0) {
            start_in_thumbnail = true;
            printf("参数检测: 将直接进入缩略图视图\n");
        } else if (strcmp(argv[i], "--slideshow") == 0) {
            start_in_slideshow = true;
            printf("参数检测: 将直接进入幻灯片播放模式\n");
        }
    }

    // 检查是否禁用GPIO访问
    char *disable_gpio = getenv("DISABLE_GPIO");
    if (disable_gpio && strcmp(disable_gpio, "1") == 0) {
        printf("检测到DISABLE_GPIO=1环境变量，将禁用GPIO访问\n");
    }

    if (initialize_app(PHOTO_DIR_PATH) != 0) return -1;

    // 根据命令行参数设置初始状态
    if (start_in_thumbnail && g_media_count > 0) {
        g_app_state = STATE_THUMBNAIL_VIEW;
        g_thumbnail_start_index = 0;
    } else if (start_in_slideshow && g_media_count > 0) {
        g_app_state = STATE_SLIDESHOW;
        g_current_media_index = 0;
        g_slideshow_is_paused = false;
    } else {
        g_app_state = STATE_MAIN_MENU;
    }

    struct input_event ev;
    int touch_x = 0, touch_y = 0;
    int swipe_start_x = -1;

    // 用于回收站双击检测
    static struct timeval recycle_bin_last_click_time = {0, 0};
    static int recycle_bin_last_clicked_page_index = -1;

    // 初始渲染
    switch (g_app_state) {
        case STATE_MAIN_MENU:
    render_main_menu();
            break;
        case STATE_THUMBNAIL_VIEW:
            render_thumbnail_view();
            break;
        case STATE_SLIDESHOW:
            render_slideshow();
            break;
        default:
            render_main_menu();
            break;
    }
    display_on_lcd();

    while (g_app_state != STATE_EXIT) {
        AppState current_state_on_loop_start = g_app_state;
        bool state_changed_by_event = false;

        if (g_is_animating) {
            usleep(20000);
            continue;
        }

        // --- 统一事件循环 ---
        // 初始化文件描述符集，监听触摸设备
        fd_set read_fds;
        struct timeval timeout_val;
        struct timeval *timeout_ptr = NULL;// 超时指针（根据状态动态设置）

        FD_ZERO(&read_fds);
        FD_SET(g_touch_fd, &read_fds);// 将触摸设备文件描述符加入监听集

        // 根据当前状态决定是否需要超时
        //幻灯片
        if (g_app_state == STATE_SLIDESHOW && !g_slideshow_is_paused) {
            timeout_val.tv_sec = 2;
            timeout_val.tv_usec = 0;
            timeout_ptr = &timeout_val;
            //回收站
        } else if (g_app_state == STATE_RECYCLE_BIN_VIEW && recycle_bin_last_clicked_page_index != -1) {
            struct timeval now;
            gettimeofday(&now, NULL);
            long elapsed_us = (now.tv_sec - recycle_bin_last_click_time.tv_sec) * 1000000L + (now.tv_usec - recycle_bin_last_click_time.tv_usec);
            long remaining_us = DOUBLE_CLICK_TIME - elapsed_us;
            if (remaining_us < 0) remaining_us = 0;
            
            timeout_val.tv_sec = remaining_us / 1000000L;
            timeout_val.tv_usec = remaining_us % 1000000L;
            timeout_ptr = &timeout_val;
        }

        int ret = select(g_touch_fd + 1, &read_fds, NULL, NULL, timeout_ptr);

        if (ret > 0) { // 触摸事件
            int flags = fcntl(g_touch_fd, F_GETFL, 0);
            fcntl(g_touch_fd, F_SETFL, flags | O_NONBLOCK);

            while(read(g_touch_fd, &ev, sizeof(ev)) > 0) {
                if (ev.type == EV_ABS && ev.code == ABS_X) touch_x = ev.value;
                if (ev.type == EV_ABS && ev.code == ABS_Y) touch_y = ev.value;

                if (ev.type == EV_KEY && ev.code == BTN_TOUCH) {
                    if (ev.value == 1) { // 按下
                        gettimeofday(&g_touch_start_time, NULL);
                        swipe_start_x = touch_x;
                    } else if (ev.value == 0 && swipe_start_x != -1) { // 抬起
                        struct timeval now;
                        gettimeofday(&now, NULL);
                        long duration_us = (now.tv_sec - g_touch_start_time.tv_sec) * 1000000L + (now.tv_usec - g_touch_start_time.tv_usec);
                        int delta_x = touch_x - swipe_start_x;

                        if (abs(delta_x) > SWIPE_THRESHOLD) { // 滑动150
                            if (g_app_state == STATE_THUMBNAIL_VIEW && g_media_count > THUMBS_PER_PAGE) {
                                if (delta_x < 0) g_thumbnail_start_index = (g_thumbnail_start_index + THUMBS_PER_PAGE) % g_media_count;
                                else g_thumbnail_start_index = (g_thumbnail_start_index - THUMBS_PER_PAGE + g_media_count) % g_media_count;
                                state_changed_by_event = true;
                            } else if (g_app_state == STATE_PHOTO_ALBUM) {
                                // 修复：为滑动增加动画效果，与点击保持一致
                                int next_index;
                                AnimationType anim_type;
                                if (delta_x < 0) { // 向左滑，下一张
                                    next_index = (g_current_media_index + 1) % g_media_count;
                                    anim_type = ANIMATE_SLIDE_LEFT;
                                } else { // 向右滑，上一张
                                    next_index = (g_current_media_index - 1 + g_media_count) % g_media_count;
                                    anim_type = ANIMATE_SLIDE_RIGHT;
                                }

                                if (g_current_media_index != next_index) {
                                    int* next_image_buffer = malloc(LCD_SIZE);
                                    if (next_image_buffer) {
                                        if (draw_bmp_to_buffer(g_media_files[next_index].path, next_image_buffer, 0, 0, false) == 0) {
                                            animate_transition(g_screen_buffer, next_image_buffer, anim_type);
                                        }
                                        free(next_image_buffer);
                                    }
                                    g_current_media_index = next_index;
                                    // 动画函数已经更新了屏幕和缓冲区，无需再次渲染
                                    state_changed_by_event = false; 
                                }

                            } else if (g_app_state == STATE_RECYCLE_BIN_VIEW && g_recycle_bin_count > THUMBS_PER_PAGE) {
                                if (delta_x < 0) g_recycle_bin_start_index = (g_recycle_bin_start_index + THUMBS_PER_PAGE) % g_recycle_bin_count;
                                else g_recycle_bin_start_index = (g_recycle_bin_start_index - THUMBS_PER_PAGE + g_recycle_bin_count) % g_recycle_bin_count;
                                state_changed_by_event = true;
                            }
                             recycle_bin_last_clicked_page_index = -1;
                             // state_changed_by_event = true; // 已在各分支内部处理
                        } else if (duration_us >= LONG_PRESS_TIME_US) { // 长按
                            int final_x = touch_x, final_y = touch_y;
                            transform_touch_coords(&final_x, &final_y);
                             if (g_app_state == STATE_THUMBNAIL_VIEW) {
                                int i;
                                for (i = 0; i < THUMBS_PER_PAGE; i++) {
                                    if (g_thumbnail_rects[i].width > 0 && is_touch_in_rect(final_x, final_y, &g_thumbnail_rects[i])) {
                                        int media_idx = (g_thumbnail_start_index + i) % g_media_count;
                                        if (delete_media_file(media_idx) == 0) state_changed_by_event = true;
                                        break;
                                    }
                                }
                            } else if (g_app_state == STATE_RECYCLE_BIN_VIEW) {
                                int i;
                                for (i = 0; i < THUMBS_PER_PAGE; i++) {
                                    if (g_thumbnail_rects[i].width > 0 && is_touch_in_rect(final_x, final_y, &g_thumbnail_rects[i])) {
                                        int media_idx = (g_recycle_bin_start_index + i) % g_recycle_bin_count;
                                        if (permanently_delete_media_file(media_idx) == 0) state_changed_by_event = true;
                                        break;
                                    }
                                }
                            }
                            recycle_bin_last_clicked_page_index = -1;
                        } else { // 点击/双击
                            int final_x = touch_x, final_y = touch_y;
                            transform_touch_coords(&final_x, &final_y);
                             if (g_app_state == STATE_MAIN_MENU) { handle_touch_main_menu(final_x, final_y); state_changed_by_event = true; }
                             else if (g_app_state == STATE_THUMBNAIL_VIEW) { handle_touch_thumbnail_view(final_x, final_y); state_changed_by_event = true; }
                             else if (g_app_state == STATE_PHOTO_ALBUM) { handle_touch_photo_album(final_x, final_y); state_changed_by_event = true; }
                             else if (g_app_state == STATE_RECYCLE_BIN_PHOTO_VIEW) { handle_touch_recycle_bin_photo_view(final_x, final_y); state_changed_by_event = true; }
                             else if (g_app_state == STATE_SLIDESHOW) {
                                  int quarter_width = LCD_WIDTH / 4;
                                  if (final_x < quarter_width) { // 最左侧: 上一张
                                      g_slideshow_is_paused = true; // 手动切换后总是暂停
                                      int next_index = g_current_media_index;
                                      do { next_index = (next_index - 1 + g_media_count) % g_media_count; } while(g_media_files[next_index].type != MEDIA_BMP && next_index != g_current_media_index);
                                      if (next_index != g_current_media_index) g_current_media_index = next_index;
                                  
                                  } else if (final_x < quarter_width * 2) { // 中间靠左: 暂停/继续
                                      g_slideshow_is_paused = !g_slideshow_is_paused;
                                      printf("Slideshow %s.\n", g_slideshow_is_paused ? "paused" : "resumed");

                                  } else if (final_x < quarter_width * 3) { // 中间靠右: 退出
                                      g_app_state = STATE_MAIN_MENU;

                                  } else { // 最右侧: 下一张
                                      g_slideshow_is_paused = true; // 手动切换后总是暂停
                                      int next_index = g_current_media_index;
                                      do { next_index = (next_index + 1) % g_media_count; } while(g_media_files[next_index].type != MEDIA_BMP && next_index != g_current_media_index);
                                      if (next_index != g_current_media_index) g_current_media_index = next_index;
                                  }
                                  state_changed_by_event = true;
                              }
                              else if (g_app_state == STATE_STANDBY) { g_app_state = STATE_MAIN_MENU; state_changed_by_event = true; }
                               else if (g_app_state == STATE_RECYCLE_BIN_VIEW) {
                                  if (is_touch_in_rect(final_x, final_y, &g_rect_return)) {
                                      g_app_state = STATE_MAIN_MENU;
                                      state_changed_by_event = true;
                                  } else {
                                      int current_page_index = -1;
                                      int i;
                                      for(i = 0; i < THUMBS_PER_PAGE; i++) {
                                          if(g_thumbnail_rects[i].width > 0 && is_touch_in_rect(final_x, final_y, &g_thumbnail_rects[i])) { current_page_index = i; break; }
                                      }
                                      if (current_page_index != -1) {
                                          long time_diff_us = (now.tv_sec - recycle_bin_last_click_time.tv_sec) * 1000000L + (now.tv_usec - recycle_bin_last_click_time.tv_usec);
                                          if (recycle_bin_last_clicked_page_index == current_page_index && time_diff_us < DOUBLE_CLICK_TIME) {
                                              int media_idx = (g_recycle_bin_start_index + current_page_index) % g_recycle_bin_count;
                                              if (restore_media_file(media_idx) == 0) state_changed_by_event = true;
                                              recycle_bin_last_clicked_page_index = -1;
                                          } else {
                                              recycle_bin_last_clicked_page_index = current_page_index;
                                              recycle_bin_last_click_time = now;
                                          }
                                      } else {
                                          recycle_bin_last_clicked_page_index = -1;
                                      }
                                  }
                              }
                        }
                        swipe_start_x = -1;
                    }
                }
            }
            fcntl(g_touch_fd, F_SETFL, flags);
        } else if (ret == 0) { // 超时
            if (g_app_state == STATE_SLIDESHOW && !g_slideshow_is_paused) {
                int next_index = g_current_media_index;
                do { next_index = (next_index + 1) % g_media_count; } while(g_media_files[next_index].type == MEDIA_AVI && next_index != g_current_media_index);
                if(next_index != g_current_media_index) g_current_media_index = next_index;
                state_changed_by_event = true;
            } else if (g_app_state == STATE_RECYCLE_BIN_VIEW && recycle_bin_last_clicked_page_index != -1) {
                int media_idx = (g_recycle_bin_start_index + recycle_bin_last_clicked_page_index) % g_recycle_bin_count;
                 if (g_recycle_bin_files[media_idx].type == MEDIA_BMP) {
                    g_current_media_index = media_idx;
                    g_app_state = STATE_RECYCLE_BIN_PHOTO_VIEW;
                 } else {
                    // 现在单击视频会直接播放
                    printf("[ACTION] Playing video from recycle bin...\n");
                    play_video_with_mplayer(g_recycle_bin_files[media_idx].path, STATE_RECYCLE_BIN_VIEW);
                 }
                recycle_bin_last_clicked_page_index = -1;
                state_changed_by_event = true;
            }
        } else {
            perror("select");
            g_app_state = STATE_EXIT;
        }

        if (g_app_state != current_state_on_loop_start || state_changed_by_event) {
             if (g_app_state == STATE_MAIN_MENU) render_main_menu();
             else if (g_app_state == STATE_THUMBNAIL_VIEW) render_thumbnail_view();
             else if (g_app_state == STATE_RECYCLE_BIN_VIEW) render_recycle_bin_view();
             else if (g_app_state == STATE_RECYCLE_BIN_PHOTO_VIEW) render_recycle_bin_photo_view();
             else if (g_app_state == STATE_PHOTO_ALBUM) { if (g_media_count > 0) render_photo_album(); else { g_app_state = STATE_MAIN_MENU; render_main_menu(); } }
             else if (g_app_state == STATE_SLIDESHOW) { if (g_media_count > 0) render_slideshow(); else { g_app_state = STATE_MAIN_MENU; render_main_menu(); } }
             else if (g_app_state == STATE_STANDBY) {
                int i;
                for(i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = MAIN_MENU_BG_COLOR;
             }
             if(g_app_state != STATE_EXIT) display_on_lcd();
        }
    }

    int i;
    for(i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x000000;
     display_on_lcd();
     cleanup_app();
     return 0;
}

int delete_media_file(int media_index) {
    if (media_index < 0 || media_index >= g_media_count) {
        fprintf(stderr, "错误: 无效的媒体索引 %d\n", media_index);
        return -1;
    }

    const char *old_path = g_media_files[media_index].path;
    const char *filename = strrchr(old_path, '/');
    if (filename == NULL) {
        filename = old_path; // 路径中没有'/'，整个都是文件名
    } else {
        filename++; // 跳过'/'字符
    }

    char new_path[512];
    snprintf(new_path, sizeof(new_path), "%s/%s", RECYCLE_BIN_PATH, filename);

    printf("准备移动 '%s' -> '%s'\n", old_path, new_path);

    if (rename(old_path, new_path) != 0) {
        perror("错误: 移动文件失败");
        return -1;
    }

    printf("文件移动成功。\n");

    // 新增: 将文件条目添加到回收站列表
    MediaType type_to_move = g_media_files[media_index].type;
    g_recycle_bin_count++;
    g_recycle_bin_files = (MediaFile *)realloc(g_recycle_bin_files, g_recycle_bin_count * sizeof(MediaFile));
    if (!g_recycle_bin_files) {
        perror("错误: realloc回收站列表失败");
        g_recycle_bin_count--;
        // 最好能把文件移回去，但暂时简化处理
        return -1;
    }
    // 注意: strdup 不是标准C99函数，但通常可用。如果编译失败，需要手动实现。
    char* new_path_dup = strdup(new_path);
    if (!new_path_dup) {
        perror("错误: strdup失败");
        g_recycle_bin_count--;
        return -1;
    }
    g_recycle_bin_files[g_recycle_bin_count - 1].path = new_path_dup;
    g_recycle_bin_files[g_recycle_bin_count - 1].type = type_to_move;


    // 释放路径字符串占用的内存
    free(g_media_files[media_index].path);

    // 从数组中移除该条目
    if (media_index < g_media_count - 1) {
        memmove(&g_media_files[media_index], &g_media_files[media_index + 1], (g_media_count - media_index - 1) * sizeof(MediaFile));
    }

    g_media_count--;

    // 清理并重新初始化缓存，以防被删除的图片仍在缓存中
    cleanup_cache();
    initialize_cache();

    // 如果起始索引超出了范围（例如删除了最后一页的所有图片），则重置到第一页
    if (g_thumbnail_start_index >= g_media_count && g_media_count > 0) {
        g_thumbnail_start_index = ((g_media_count - 1) / THUMBS_PER_PAGE) * THUMBS_PER_PAGE;
    } else if (g_media_count == 0) {
        g_thumbnail_start_index = 0;
    }
    
    // 确保当前选中的图片索引仍然有效
    if (g_current_media_index >= g_media_count) {
        g_current_media_index = 0;
    }

    return 0;
}

int permanently_delete_media_file(int recycle_bin_index) {
    if (recycle_bin_index < 0 || recycle_bin_index >= g_recycle_bin_count) {
        return -1;
    }

    const char *path = g_recycle_bin_files[recycle_bin_index].path;
    if (remove(path) != 0) {
        perror("永久删除文件失败");
        return -1;
    }

    free(g_recycle_bin_files[recycle_bin_index].path);
    if (recycle_bin_index < g_recycle_bin_count - 1) {
        memmove(&g_recycle_bin_files[recycle_bin_index], &g_recycle_bin_files[recycle_bin_index + 1], (g_recycle_bin_count - recycle_bin_index - 1) * sizeof(MediaFile));
    }
    g_recycle_bin_count--;

    cleanup_cache();
    initialize_cache();

    if (g_recycle_bin_start_index >= g_recycle_bin_count && g_recycle_bin_count > 0) {
        g_recycle_bin_start_index = ((g_recycle_bin_count - 1) / THUMBS_PER_PAGE) * THUMBS_PER_PAGE;
    } else if (g_recycle_bin_count == 0) {
        g_recycle_bin_start_index = 0;
    }
    return 0;
}

int restore_media_file(int recycle_bin_index) {
    if (recycle_bin_index < 0 || recycle_bin_index >= g_recycle_bin_count) {
        return -1;
    }

    const char *old_path = g_recycle_bin_files[recycle_bin_index].path;
    const char *filename = strrchr(old_path, '/');
    filename = (filename == NULL) ? old_path : filename + 1;

    char new_path[512];
    snprintf(new_path, sizeof(new_path), "%s/%s", PHOTO_DIR_PATH, filename);

    if (rename(old_path, new_path) != 0) {
        perror("恢复文件失败");
        return -1;
    }
    
    // 1. 添加到主列表
    MediaType type_to_move = g_recycle_bin_files[recycle_bin_index].type;
    g_media_count++;
    g_media_files = (MediaFile*)realloc(g_media_files, g_media_count * sizeof(MediaFile));
    if(!g_media_files) return -1;
    char* final_path = strdup(new_path);
    if(!final_path) return -1;
    g_media_files[g_media_count-1].path = final_path;
    g_media_files[g_media_count-1].type = type_to_move;

    // 2. 从回收站列表移除
    free(g_recycle_bin_files[recycle_bin_index].path);
    if (recycle_bin_index < g_recycle_bin_count - 1) {
        memmove(&g_recycle_bin_files[recycle_bin_index], &g_recycle_bin_files[recycle_bin_index + 1], (g_recycle_bin_count - recycle_bin_index - 1) * sizeof(MediaFile));
    }
    g_recycle_bin_count--;
    
    cleanup_cache();
    initialize_cache();

    if (g_recycle_bin_start_index >= g_recycle_bin_count && g_recycle_bin_count > 0) {
        g_recycle_bin_start_index = ((g_recycle_bin_count - 1) / THUMBS_PER_PAGE) * THUMBS_PER_PAGE;
    } else if (g_recycle_bin_count == 0) {
        g_recycle_bin_start_index = 0;
    }
    return 0;
}

// =================================================================================
// MPlayer 视频播放核心
// =================================================================================
#include <sys/wait.h>

void play_video_with_mplayer(const char* video_path, AppState return_to_state) {
    const char *fifo_path = "/tmp/mplayer_fifo";
    mkfifo(fifo_path, 0666); // 创建命名管道

    printf("[VIDEO] Forking to play '%s'\n", video_path);
    pid_t pid = fork();
    if (pid == -1) {
        perror("fork 失败");
        return;
    }

    if (pid == 0) { // 子进程
        // 关闭标准输入输出和错误流，防止 mplayer 输出扰乱终端
        close(STDIN_FILENO);
        close(STDOUT_FILENO);
        close(STDERR_FILENO);

        // 启动 mplayer
        printf("[VIDEO] Child PID %d executing mplayer.\n", getpid());
        execlp("mplayer", "mplayer", "-slave", "-quiet",
               "-input", "file=/tmp/mplayer_fifo",
               "-vo", "fbdev2:/dev/fb0", "-vf", "scale=800:480",
               "-loop", "0",
               video_path, NULL);
        
        // 如果 execlp 返回, 说明出错了
        perror("execlp mplayer 失败");
        exit(1);
    } else { // 父进程
        int fifo_fd = -1;
        struct input_event ev;
        struct timeval last_click_time = {0, 0};
        int original_flags;
        
        printf("[VIDEO] Parent (PID: %d) is managing child (PID: %d)\n", getpid(), pid);

        // 保存触摸屏fd的当前状态，并强制设为阻塞模式，以确保此处的read能正常工作
        original_flags = fcntl(g_touch_fd, F_GETFL, 0);
        if (original_flags & O_NONBLOCK) {
            fcntl(g_touch_fd, F_SETFL, original_flags & ~O_NONBLOCK);
        }

        // 稍等一下，确保mplayer已经启动并创建了窗口
        usleep(500000); // 0.5秒
        fifo_fd = open(fifo_path, O_WRONLY);
        if (fifo_fd == -1) {
            perror("[VIDEO-ERROR] 打开 fifo 失败");
            kill(pid, SIGKILL); // 杀掉子进程
            waitpid(pid, NULL, 0);
            fcntl(g_touch_fd, F_SETFL, original_flags);
            unlink(fifo_path);
            g_app_state = return_to_state;
            return;
        }
        printf("[VIDEO] FIFO pipe opened. Ready to send commands.\n");

        while(read(g_touch_fd, &ev, sizeof(ev)) > 0) {
            if (ev.type == EV_KEY && ev.code == BTN_TOUCH && ev.value == 0) { // 手指抬起
                struct timeval current_click_time;
                gettimeofday(&current_click_time, NULL);

                long time_diff_us = (current_click_time.tv_sec - last_click_time.tv_sec) * 1000000L +
                                    (current_click_time.tv_usec - last_click_time.tv_usec);
                
                if (time_diff_us < DOUBLE_CLICK_TIME) { // 双击
                    printf("[VIDEO] Double click detected. Sending 'pause' to pipe.\n");
                    write(fifo_fd, "pause\n", 6);
                    // 重置时间避免三击等情况
                    last_click_time.tv_sec = 0;
                    last_click_time.tv_usec = 0;
                } else { // 单击
                    printf("[VIDEO] Single click detected. Quitting video.\n");
                    write(fifo_fd, "quit\n", 5);
                    break; // 退出循环
                }
                last_click_time = current_click_time;
            }
        }
        
        // 清理
        printf("[VIDEO] Exiting touch loop for player. Cleaning up process %d.\n", pid);
        if(fifo_fd != -1) close(fifo_fd);

        // 在等待子进程或执行任何其他操作之前，恢复触摸屏fd的原始状态
        fcntl(g_touch_fd, F_SETFL, original_flags);

        waitpid(pid, NULL, 0); // 等待 mplayer 进程结束
        printf("[VIDEO] Child process %d finished. Unlinking FIFO.\n", pid);
        unlink(fifo_path); // 删除管道文件

        // 正确返回到之前的状态
        g_app_state = return_to_state;
        printf("[STATE] Video player exited. Returning to state %d\n", return_to_state);
    }
}

// =================================================================================
// 动画函数
// =================================================================================

void animate_transition(int *from_buffer, int *to_buffer, AnimationType type) {
    int step, y, offset;
    void *fbp;

    if (type == ANIMATE_NONE) {
        memcpy(g_screen_buffer, to_buffer, LCD_SIZE);
        display_on_lcd();
        return;
    }

    g_is_animating = true;

    fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) {
        perror("mmap failed in animation");
        memcpy(g_screen_buffer, to_buffer, LCD_SIZE); // 回退到无动画模式
        display_on_lcd();
        g_is_animating = false;
        return;
    }

    for (step = 1; step <= ANIMATION_STEPS; step++) {
        offset = (LCD_WIDTH * step) / ANIMATION_STEPS;
        for (y = 0; y < LCD_HEIGHT; y++) {
            int *dest_row = (int *)fbp + y * LCD_WIDTH;
            int *from_row = from_buffer + y * LCD_WIDTH;
            int *to_row = to_buffer + y * LCD_WIDTH;

            if (type == ANIMATE_SLIDE_LEFT) {
                if (offset < LCD_WIDTH) {
                    memcpy(dest_row, from_row + offset, (LCD_WIDTH - offset) * sizeof(int));
                }
                memcpy(dest_row + (LCD_WIDTH - offset), to_row, offset * sizeof(int));
            } else if (type == ANIMATE_SLIDE_RIGHT) {
                memcpy(dest_row, to_row + (LCD_WIDTH - offset), offset * sizeof(int));
                 if (offset < LCD_WIDTH) {
                    memcpy(dest_row + offset, from_row, (LCD_WIDTH - offset) * sizeof(int));
                }
            }
        }
        usleep(ANIMATION_DELAY_US);
    }

    memcpy(fbp, to_buffer, LCD_SIZE);
    munmap(fbp, LCD_SIZE);

    memcpy(g_screen_buffer, to_buffer, LCD_SIZE);
    g_is_animating = false;
}

// 扫描媒体目录，加载所有媒体文件
void scan_directory(const char* dir) {
    g_media_files = get_media_files_from_dir(dir, &g_media_count);
    if (g_media_count == 0) {
        fprintf(stderr, "警告: 在照片目录 '%s' 中未找到任何 .bmp 或 .avi 文件。\n", dir);
    } else {
        printf("信息: 从 '%s' 成功加载了 %d 个媒体文件。\n", dir, g_media_count);
    }
}

// 扫描回收站目录，加载所有回收站文件
void scan_recycle_bin(void) {
    // 尝试创建回收站目录，忽略已存在时的错误
    if (mkdir(RECYCLE_BIN_PATH, 0755) != 0 && errno != EEXIST) {
        perror("警告: 创建回收站目录失败");
    } else {
        printf("信息: 回收站目录 '%s' 已就绪。\n", RECYCLE_BIN_PATH);
    }
    
    // 加载回收站文件
    g_recycle_bin_files = get_media_files_from_dir(RECYCLE_BIN_PATH, &g_recycle_bin_count);
    if (g_recycle_bin_count > 0) {
        printf("信息: 从回收站 '%s' 成功加载了 %d 个文件。\n", RECYCLE_BIN_PATH, g_recycle_bin_count);
    }
}

// 注释掉 draw_voice_button 函数内容，使其为空实现
void draw_voice_button(int* buffer) {
    // 不再绘制任何内容，按钮不可见，仅保留功能区域
}
