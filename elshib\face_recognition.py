import cv2
import os
import numpy as np
import time
from model_loader import get_face_detector

class FaceRecognizer:
    def __init__(self, threshold: float = 0.5, template_size: tuple = (128, 128)) -> None:
        """
        人脸识别器初始化
        :param threshold: 匹配阈值(0-1)，高于此值认为匹配成功, 默认0.7
        :param template_size: 模板图像尺寸 (宽度, 高度), 默认(128, 128)
        """
        self.threshold: float = threshold
        self.template_size: tuple = template_size
        self.templates: dict[str, np.ndarray] = {}
        
        # 加载DNN人脸检测器。
        # 移除此处的try-except，让初始化失败的异常可以被主程序块捕获，从而提供更清晰的错误信息。
        self.face_detector, self.confidence_threshold = get_face_detector(confidence_threshold=0.6)


    def load_templates(self, template_dir: str) -> None:
        """
        从模板目录加载所有模板图像。
        :param template_dir: 包含模板图像的目录路径。
        """
        if not os.path.isdir(template_dir):
            # 如果目录不存在，只打印警告而不是抛出异常，因为可能在后面创建
            print(f"警告: 模板目录 '{template_dir}' 不存在。")
            self.templates = {}
            return

        self.templates = {}
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp')
        template_files = [f for f in os.listdir(template_dir) if f.lower().endswith(image_extensions)]
        
        print(f"调试信息: 在 {template_dir} 中找到 {len(template_files)} 个图像文件: {template_files}")

        for filename in template_files:
            name = os.path.splitext(filename)[0]
            template_path = os.path.join(template_dir, filename)
            
            try:
                # 模板已经是标准的128x128灰度图，直接加载
                template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
                if template is None:
                    print(f"警告: 无法加载模板图像 {template_path}，文件可能已损坏或格式不受支持。")
                    continue
            except Exception as e:
                print(f"错误: 加载模板 {filename} 时发生异常 - {e}")
                continue
            
            self.templates[name] = template
            print(f"成功加载模板: {name} (来自 {filename})")
        
        if not self.templates:
            print("警告: 未能加载任何有效的模板图像。")
        else:
            print(f"成功加载了 {len(self.templates)} 个模板。")

    def process_image(self, image_path: str, output_path: str = None) -> tuple[np.ndarray, list[str]]:
        """
        处理单张图像，识别人脸、与模板匹配并进行标注。
        :param image_path: 输入图像的路径。
        :param output_path: 结果图像的保存路径。如果为None，则不保存。
        :return: 一个元组，包含处理后的图像和识别出的人名列表。
        """
        if self.face_detector is None:
            raise RuntimeError("人脸检测器未成功初始化，无法处理图像。")

        image = cv2.imread(image_path)
        if image is None:
            raise IOError(f"无法加载图像文件: {image_path}")

        result_image = image.copy()
        (h, w) = image.shape[:2]
        
        # 将存储识别结果的列表
        recognized_names = []

        # --- 使用DNN进行人脸检测 ---
        blob = cv2.dnn.blobFromImage(cv2.resize(image, (300, 300)), 1.0,
                                     (300, 300), (104.0, 177.0, 123.0))
        self.face_detector.setInput(blob)
        detections = self.face_detector.forward()
        
        print(f"在 {os.path.basename(image_path)} 中检测到 {detections.shape[2]} 个潜在人脸区域")
        
        # 遍历所有检测到的人脸
        for i in range(0, detections.shape[2]):
            confidence = detections[0, 0, i, 2]

            if confidence > self.confidence_threshold:
                # 计算边界框
                box = detections[0, 0, i, 3:7] * np.array([w, h, w, h])
                (startX, startY, endX, endY) = box.astype("int")
                
                # 确保边界框在图像范围内
                (startX, startY) = (max(0, startX), max(0, startY))
                (endX, endY) = (min(w - 1, endX), min(h - 1, endY))

                # --- 核心识别逻辑 ---
                # 从原始彩色图中裁剪人脸，转为灰度图用于匹配
                face = image[startY:endY, startX:endX]
                if face.size == 0:
                    continue
                
                gray_face = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)
                resized_face = cv2.resize(gray_face, self.template_size)
                
                best_match_name = "Unknown"
                best_match_score = 0.0
                template_scores = {}
                
                if not self.templates:
                    print("警告: 模板库为空，无法进行匹配。")
                else:
                    for name, template in self.templates.items():
                        result = cv2.matchTemplate(resized_face, template, cv2.TM_CCOEFF_NORMED)
                        _, max_val, _, _ = cv2.minMaxLoc(result)
                        template_scores[name] = max_val
                        
                        if max_val > best_match_score:
                            best_match_score = max_val
                            best_match_name = name if max_val >= self.threshold else "Unknown"
                
                # 将本次识别结果添加到列表中
                recognized_names.append(best_match_name)

                # --- 绘制结果 ---
                color = (0, 255, 0) if best_match_name != "Unknown" else (0, 0, 255)
                cv2.rectangle(result_image, (startX, startY), (endX, endY), color, 2)
                
                label = f"{best_match_name} ({best_match_score:.2f})"
                label_size, base_line = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)
                
                top = max(startY, label_size[1] + 10)
                cv2.rectangle(result_image, (startX, top - label_size[1] - 10),
                              (startX + label_size[0], top - 5), (255, 255, 255), cv2.FILLED)
                cv2.putText(result_image, label, (startX, top - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                
                print(f"  - 人脸位置: ({startX}, {startY}, {endX-startX}, {endY-startY}), 识别结果: {best_match_name}, 最高分: {best_match_score:.2f}")
                if self.templates and best_match_name != "Unknown":
                    for name, score in sorted(template_scores.items(), key=lambda item: item[1], reverse=True):
                        print(f"    与 {name} 的匹配分数: {score:.4f}")
        
        if output_path:
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            cv2.imwrite(output_path, result_image)
            print(f"结果图像已保存至: {output_path}")
        
        return result_image, recognized_names

if __name__ == "__main__":
    # --- 配置参数 ---
    TEMPLATE_DIR = "templates_cropped"
    TEST_IMAGE_DIR = "D:\\desktop\\share\\jpg"
    OUTPUT_DIR = "output"
    THRESHOLD = 0.8  # 设置为0.8以提高识别严格度，低于此值显示Unknown

    # --- 初始化目录 ---
    for dir_path in [TEMPLATE_DIR, OUTPUT_DIR]: # TEST_IMAGE_DIR由用户指定，不在此创建
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"已创建目录: {dir_path}")

    try:
        recognizer = FaceRecognizer(threshold=THRESHOLD)
        recognizer.load_templates(TEMPLATE_DIR)
        
        if not recognizer.templates:
            print(f"\n模板目录 '{TEMPLATE_DIR}' 为空。")
            print("请先运行 'create_templates.py' 生成标准人脸模板。")
        else:
            if not os.path.isdir(TEST_IMAGE_DIR):
                print(f"\n错误：测试目录 '{TEST_IMAGE_DIR}' 不存在或不是一个目录。")
            else:
                # --- 新增：持续监控与处理 ---
                print(f"\n--- 开始持续监控目录: '{TEST_IMAGE_DIR}' ---")
                last_processed_file_mtime = 0
                
                while True:
                    try:
                        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif')
                        all_images = [os.path.join(TEST_IMAGE_DIR, f) 
                                      for f in os.listdir(TEST_IMAGE_DIR) 
                                      if f.lower().endswith(image_extensions)]
                        
                        if not all_images:
                            # 目录为空，等待
                            time.sleep(2)
                            continue
                        
                        # 找到最新的文件
                        newest_image_path = max(all_images, key=os.path.getmtime)
                        newest_image_mtime = os.path.getmtime(newest_image_path)
                        
                        # 如果有比上次处理过的更新的文件，则处理
                        if newest_image_mtime > last_processed_file_mtime:
                            print(f"\n检测到新文件或更新文件: {newest_image_path}")
                            
                            last_processed_file_mtime = newest_image_mtime

                            image_file = os.path.basename(newest_image_path)
                            output_path = os.path.join(OUTPUT_DIR, image_file)
                            
                            _, recognized_names = recognizer.process_image(newest_image_path, output_path)
                            
                            if recognized_names:
                                RESULT_FILE_PATH = os.path.join(TEST_IMAGE_DIR, "result.txt")
                                print(f"将识别结果 {recognized_names} 写入 {RESULT_FILE_PATH}")
                                
                                output_lines = []
                                for i in range(0, len(recognized_names), 2):
                                    pair = recognized_names[i:i+2]
                                    output_lines.append(" ".join(pair))
                                
                                content_to_write = "\n".join(output_lines)
                                
                                # 在追加模式下，如果文件非空，先添加一个换行符，确保新内容在新的一行
                                # 这样可以保证每次写入都是独立的，且文件末尾不会有多余的空行
                                prefix = ""
                                if os.path.exists(RESULT_FILE_PATH) and os.path.getsize(RESULT_FILE_PATH) > 0:
                                    prefix = "\n"
                                
                                with open(RESULT_FILE_PATH, "a", encoding="utf-8") as f:
                                    f.write(prefix + content_to_write)
                            else:
                                print("在新图片中未识别出任何人脸。")

                            print("\n--- 监控中... 等待下一个新文件... ---")
                        
                        # 短暂休眠，避免CPU占用过高
                        time.sleep(2)
                    
                    except FileNotFoundError:
                        print("警告：在检查文件时，文件被删除。将继续监控。")
                        time.sleep(2)
                    except Exception as loop_error:
                        print(f"监控循环中发生错误: {loop_error}")
                        time.sleep(5) # 发生错误后等待更长时间

    except IOError as e:
        print(f"\n初始化失败: {e}")
        print("这通常是由于网络问题导致无法下载所需的模型文件。")
        print("请检查您的网络连接、防火墙或代理设置，然后重试。")
    except KeyboardInterrupt:
        print("\n程序被用户中断。正在退出...")
    except Exception as e:
        print(f"\n程序运行期间发生未预料的错误: {e}")