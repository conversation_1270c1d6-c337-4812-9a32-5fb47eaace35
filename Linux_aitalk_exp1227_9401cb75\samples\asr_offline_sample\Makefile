# 编译环境设置
CC = gcc
ARM_CC = arm-linux-gcc  # 用于交叉编译客户端的编译器

# 目录定义
TARGET_DIR = ../../bin
INCLUDE_DIR = ../../include
LIB_DIR = ../../libs/x64  # 假设服务器为x64架构

# 源文件
SERVER_SRC = recognition_server.c
CLIENT_SRC = client.c

# 目标文件
SERVER_EXE = $(TARGET_DIR)/recognition_server
CLIENT_EXE = $(TARGET_DIR)/client

# 编译和链接参数
# -I 指定头文件目录
# -L 指定库文件目录
# -l 指定具体链接的库 (例如 -lmsc 会链接 libmsc.so)
CFLAGS = -I$(INCLUDE_DIR)
LIBS = -L$(LIB_DIR) -lmsc -lrt -ldl -lpthread

# 默认目标，执行make时会执行此目标
all: $(SERVER_EXE) $(CLIENT_EXE)

# 编译服务器程序
$(SERVER_EXE): $(SERVER_SRC)
	@mkdir -p $(TARGET_DIR) # 确保目标目录存在
	$(CC) $(SERVER_SRC) -o $@ $(CFLAGS) $(LIBS)
	@echo "编译成功: $(SERVER_EXE)"

# 编译客户端程序
$(CLIENT_EXE): $(CLIENT_SRC)
	@mkdir -p $(TARGET_DIR) # 确保目标目录存在
	$(ARM_CC) $(CLIENT_SRC) -o $@
	@echo "编译成功: $(CLIENT_EXE)"

# 清理生成的文件
clean:
	@rm -f $(SERVER_EXE) $(CLIENT_EXE)
	@echo "清理完毕"

# .PHONY 避免与同名文件冲突
.PHONY: all clean
