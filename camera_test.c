#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <linux/input.h>
#include <sys/stat.h>
#include <dirent.h>
#include <ctype.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#define CAMERA_INPUT_DEVICE "/dev/input/event5"
#define SCREENSHOT_DIR "/root/bmpaaa"
#define VM_IP "*************" // 请确保这是您虚拟机的正确IP地址
#define VM_PORT 54322

#pragma pack(1)
typedef struct {
    unsigned char signature[2];
    unsigned int fileSize;
    unsigned short reserved1;
    unsigned short reserved2;
    unsigned int dataOffset;
} BMPFileHeader;

typedef struct {
    unsigned int size;
    int width;
    int height;
    unsigned short planes;
    unsigned short bitCount;
    unsigned int compression;
    unsigned int imageSize;
    int xPixelsPerMeter;
    int yPixelsPerMeter;
    unsigned int colorsUsed;
    unsigned int colorsImportant;
} BMPInfoHeader;
#pragma pack()

void send_image_to_vm(const char* filepath) {
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("错误: 无法创建套接字");
        return;
    }

    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(VM_PORT);
    if (inet_pton(AF_INET, VM_IP, &server_addr.sin_addr) <= 0) {
        perror("错误: 无效的IP地址或地址族");
        close(sockfd);
        return;
    }

    printf("正在连接到虚拟机 %s:%d...\n", VM_IP, VM_PORT);
    if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("错误: 连接到虚拟机失败");
        close(sockfd);
        return;
    }

    printf("连接成功。正在发送文件: %s\n", filepath);

    int fd = open(filepath, O_RDONLY);
    if (fd == -1) {
        perror("错误: 无法打开BMP文件进行发送");
        close(sockfd);
        return;
    }

    char buffer[4096];
    ssize_t bytes_read;
    while ((bytes_read = read(fd, buffer, sizeof(buffer))) > 0) {
        if (write(sockfd, buffer, bytes_read) != bytes_read) {
            perror("错误: 发送文件失败");
            break;
        }
    }

    if (bytes_read < 0) {
        perror("错误: 读取文件进行发送时失败");
    }

    close(fd);
    close(sockfd);
    printf("文件发送完毕，连接已关闭。\n");
}


int save_framebuffer_to_bmp(const char* filepath) {
    const int width = 800;
    const int height = 480;
    const int bpp = 4;
    const int screen_size = width * height * bpp;

    int fb_fd = open("/dev/fb0", O_RDONLY);
    if (fb_fd == -1) {
        perror("错误: 无法打开framebuffer设备 /dev/fb0");
        return -1;
    }

    void* fb_mem = mmap(NULL, screen_size, PROT_READ, MAP_SHARED, fb_fd, 0);
    if (fb_mem == MAP_FAILED) {
        perror("错误: 映射framebuffer内存失败");
        close(fb_fd);
        return -1;
    }

    FILE* f = fopen(filepath, "wb");
    if (!f) {
        perror("错误: 无法创建BMP文件");
        munmap(fb_mem, screen_size);
        close(fb_fd);
        return -1;
    }

    int row_padded = (width * 3 + 3) & ~3;
    int image_size = row_padded * height;

    BMPFileHeader fh;
    fh.signature[0] = 'B';
    fh.signature[1] = 'M';
    fh.fileSize = 54 + image_size;
    fh.reserved1 = 0;
    fh.reserved2 = 0;
    fh.dataOffset = 54;

    BMPInfoHeader fih;
    fih.size = sizeof(BMPInfoHeader);
    fih.width = width;
    fih.height = height;
    fih.planes = 1;
    fih.bitCount = 24;
    fih.compression = 0;
    fih.imageSize = image_size;
    fih.xPixelsPerMeter = 0;
    fih.yPixelsPerMeter = 0;
    fih.colorsUsed = 0;
    fih.colorsImportant = 0;

    fwrite(&fh, sizeof(fh), 1, f);
    fwrite(&fih, sizeof(fih), 1, f);

    unsigned char* row_data = (unsigned char*)malloc(row_padded);
    int* screen_buffer = (int*)fb_mem;
    int x, y;

    for (y = height - 1; y >= 0; y--) {
        for (x = 0; x < width; x++) {
            int pixel_color = screen_buffer[y * width + x];
            row_data[x * 3] = pixel_color & 0xFF;
            row_data[x * 3 + 1] = (pixel_color >> 8) & 0xFF;
            row_data[x * 3 + 2] = (pixel_color >> 16) & 0xFF;
        }
        fwrite(row_data, 1, row_padded, f);
    }

    free(row_data);
    fclose(f);
    munmap(fb_mem, screen_size);
    close(fb_fd);

    return 0;
}

void ensure_directory_exists(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == 0) {
            printf("目录 '%s' 已创建。\n", path);
        } else {
            perror("创建目录失败");
        }
    }
}

int get_next_filenumber(const char* dir_path) {
    int max_num = 0;
    DIR* d = opendir(dir_path);
    if (!d) {
        return 1;
    }
    
    struct dirent* entry;
    while ((entry = readdir(d)) != NULL) {
        char* bmp_ext = strstr(entry->d_name, ".bmp");
        if (entry->d_type == DT_REG && bmp_ext && bmp_ext[4] == '\0') {
             int is_numeric = 1;
             char* p;
             for (p = entry->d_name; p < bmp_ext; ++p) {
                 if (!isdigit((unsigned char)*p)) {
                     is_numeric = 0;
                     break;
                 }
             }
             if (is_numeric) {
                 int num = atoi(entry->d_name);
                 if (num > max_num) {
                     max_num = num;
                 }
             }
        }
    }
    closedir(d);
    return max_num + 1;
}

int main() {
    ensure_directory_exists(SCREENSHOT_DIR);

    int screenshot_counter = get_next_filenumber(SCREENSHOT_DIR);
    printf("截图将从 %s/%d.bmp 开始保存。\n", SCREENSHOT_DIR, screenshot_counter);

    pid_t mplayer_pid = fork();

    if (mplayer_pid < 0) {
        perror("fork 失败");
        return 1;
    }

    if (mplayer_pid == 0) {
        printf("子进程: 正在启动 mplayer...\n");
        
        execlp("mplayer", "mplayer", "tv://",
               "-tv", "driver=v4l2:device=/dev/video0:width=800:height=480:fps=15",
               "-vo", "fbdev2:/dev/fb0",
               "-vf", "scale=800:480",
               "-nosound",
               "-quiet",
               NULL);

        perror("execlp 启动 mplayer 失败");
        exit(1);

    } else {
        printf("父进程: mplayer 已启动 (PID: %d).\n", mplayer_pid);
        printf("屏幕上应有摄像头画面。\n");
        printf("\n*** 按下摄像头上的物理按键进行拍照 (保存为BMP) ***");
        printf("\n*** 在本终端按回车键 (Enter) 退出 ***\n\n");

        int cam_btn_fd = open(CAMERA_INPUT_DEVICE, O_RDONLY | O_NONBLOCK);
        if (cam_btn_fd == -1) {
            perror("警告: 无法打开摄像头按键设备, 拍照功能不可用");
        }

        fd_set fds;
        struct input_event ev;

        while (1) {
            FD_ZERO(&fds);
            FD_SET(STDIN_FILENO, &fds);
            if (cam_btn_fd != -1) {
                FD_SET(cam_btn_fd, &fds);
            }

            int max_fd = (cam_btn_fd > STDIN_FILENO) ? cam_btn_fd : STDIN_FILENO;
            
            select(max_fd + 1, &fds, NULL, NULL, NULL);

            if (cam_btn_fd != -1 && FD_ISSET(cam_btn_fd, &fds)) {
                if (read(cam_btn_fd, &ev, sizeof(ev)) == sizeof(ev)) {
                    if (ev.type == EV_KEY && ev.value == 1) {
                        printf("检测到摄像头按键! 暂停视频并截图...\n");
                        
                        // 暂停 mplayer 进程
                        kill(mplayer_pid, SIGSTOP);

                        char dest_path[256];
                        sprintf(dest_path, "%s/%d.bmp", SCREENSHOT_DIR, screenshot_counter);

                        if (save_framebuffer_to_bmp(dest_path) == 0) {
                            printf("截图已成功保存到: %s\n", dest_path);
                            
                            // 新增功能：将图片发送到虚拟机
                            send_image_to_vm(dest_path);
                            
                            screenshot_counter++;
                        } else {
                            fprintf(stderr, "错误: 保存截图失败。\n");
                        }
                        
                        // 恢复 mplayer 进程
                        kill(mplayer_pid, SIGCONT);
                        printf("视频已恢复。\n");
                    }
                }
            }

            if (FD_ISSET(STDIN_FILENO, &fds)) {
                getchar();
                break;
            }
        }
        
        if (cam_btn_fd != -1) close(cam_btn_fd);

        printf("正在停止 mplayer 进程 (PID: %d)...\n", mplayer_pid);
        kill(mplayer_pid, SIGKILL);
        waitpid(mplayer_pid, NULL, 0);
        printf("mplayer 进程已停止。\n");
    }

    return 0;
} 