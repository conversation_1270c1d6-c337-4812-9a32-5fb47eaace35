# ===================================================================
# Merged Makefile for All Applications
# ===================================================================

# --- 编译器定义 ---
# HOST_CC 用于编译在x86服务器上运行的程序
# ARM_CC 用于交叉编译在ARM开发板上运行的程序
HOST_CC = gcc
ARM_CC = arm-linux-gcc

# --- 编译参数 ---
# 通用编译参数
CFLAGS_COMMON = -g -Wall
# ARM程序专用编译参数
CFLAGS_ARM = $(CFLAGS_COMMON) -std=c99
# 服务器程序专用头文件路径
CFLAGS_SERVER = -I./Linux_aitalk_exp1227_9401cb75/include

# --- 目录和库定义 ---
# 目标输出目录
TARGET_DIR = ./Linux_aitalk_exp1227_9401cb75/bin
# 服务器依赖的库文件目录和库
LIB_DIR_HOST = ./Linux_aitalk_exp1227_9401cb75/libs/x64
LIBS_SERVER = -L$(LIB_DIR_HOST) -lmsc -lrt -ldl -lpthread
# main_app 依赖的库
LDFLAGS_MAIN = -lm

# --- 源文件路径定义 ---
# 根目录下的应用源文件
SRCS_MAIN = main.c
SRCS_ALBUM = chumuo.c
SRCS_VIDEO = video_player.c
SRCS_IMAGE = image_receiver.c

# asr_offline_sample 子目录下的源文件
ASR_SAMPLE_DIR = ./Linux_aitalk_exp1227_9401cb75/samples/asr_offline_sample
SRCS_SERVER = $(ASR_SAMPLE_DIR)/recognition_server.c
SRCS_CLIENT = $(ASR_SAMPLE_DIR)/client.c

# --- 目标文件路径定义 ---
# 根目录下的应用目标
TARGET_MAIN = main_app
TARGET_ALBUM = album_app
TARGET_VIDEO = video_app

# asr_offline_sample 的应用目标
TARGET_SERVER = $(TARGET_DIR)/recognition_server
TARGET_CLIENT = $(TARGET_DIR)/client
TARGET_IMAGE = $(TARGET_DIR)/image_re

# ===================================================================
# 编译规则
# ===================================================================

# 默认目标: all (编译所有程序)
all: $(TARGET_MAIN) $(TARGET_ALBUM) $(TARGET_VIDEO) $(TARGET_SERVER) $(TARGET_CLIENT) $(TARGET_IMAGE)

# --- 编译 ARM 应用 (根目录) ---
$(TARGET_MAIN): $(SRCS_MAIN)
	$(ARM_CC) $(CFLAGS_ARM) -o $@ $^ $(LDFLAGS_MAIN)
	chmod 777 $@
	@echo "ARM 应用 [$(TARGET_MAIN)] 编译完成!"

$(TARGET_ALBUM): $(SRCS_ALBUM)
	$(ARM_CC) $(CFLAGS_ARM) -o $@ $^
	chmod 777 $@
	@echo "ARM 应用 [$(TARGET_ALBUM)] 编译完成!"

$(TARGET_VIDEO): $(SRCS_VIDEO)
	$(ARM_CC) $(CFLAGS_ARM) -o $@ $^
	chmod 777 $@
	@echo "ARM 应用 [$(TARGET_VIDEO)] 编译完成!"

# --- 编译服务器和客户端 (asr_offline_sample) ---

# 订单相关性: $(TARGET_DIR) 是一个伪目标，确保在编译前创建目录
# 使用 | 分隔，表示 $(TARGET_DIR) 只是前提，不参与命令行的 $^
$(TARGET_SERVER) $(TARGET_CLIENT) $(TARGET_IMAGE): | $(TARGET_DIR)

$(TARGET_DIR):
	@mkdir -p $(TARGET_DIR)

# 编译服务器 (Host GCC)
$(TARGET_SERVER): $(SRCS_SERVER)
	$(HOST_CC) $(CFLAGS_COMMON) $(CFLAGS_SERVER) -o $@ $^ $(LIBS_SERVER)
	@echo "服务器程序 [$(TARGET_SERVER)] 编译完成!"

# 编译图片接收器 (Host GCC)
$(TARGET_IMAGE): $(SRCS_IMAGE)
	$(HOST_CC) $(CFLAGS_COMMON) -o $@ $^
	@echo "图片接收程序 [$(TARGET_IMAGE)] 编译完成!"

# 编译客户端 (ARM GCC)
$(TARGET_CLIENT): $(SRCS_CLIENT)
	$(ARM_CC) $(CFLAGS_ARM) -o $@ $^
	@echo "客户端程序 [$(CLIENT_EXE)] 编译完成!"

# ===================================================================
# 清理规则
# ===================================================================
# 清理所有生成的文件
clean:
	@echo "正在清理所有生成的文件..."
	rm -f $(TARGET_MAIN) $(TARGET_ALBUM) $(TARGET_VIDEO)
	rm -f $(TARGET_SERVER) $(TARGET_CLIENT) $(TARGET_IMAGE)
	@echo "清理完成。"

# 将 all 和 clean 声明为伪目标
.PHONY: all clean $(TARGET_DIR) 