import cv2
import os
import numpy as np
from model_loader import get_face_detector

# --- 配置 ---
# 存放原始照片的文件夹 (您的人脸照片)
SOURCE_IMAGE_DIR = "test_images"
# 存放处理后标准人脸模板的文件夹
CROPPED_TEMPLATE_DIR = "templates_cropped"
# 标准模板的输出尺寸
OUTPUT_SIZE = (128, 128)
# DNN检测器的置信度阈值
CONFIDENCE_THRESHOLD = 0.6

def create_templates_from_images():
    """
    从源图像目录中检测人脸，裁剪、调整尺寸，并保存为标准的人脸模板。
    """
    # 检查并创建输出目录
    if not os.path.exists(CROPPED_TEMPLATE_DIR):
        os.makedirs(CROPPED_TEMPLATE_DIR)
        print(f"已创建用于存放标准模板的目录: {CROPPED_TEMPLATE_DIR}")

    # 加载DNN人脸检测器。移除try-except，让主程序块处理初始化异常。
    face_detector, _ = get_face_detector(confidence_threshold=CONFIDENCE_THRESHOLD)

    # 获取所有源图片文件
    image_extensions = ('.jpg', '.jpeg', '.png', '.bmp')
    source_images = [f for f in os.listdir(SOURCE_IMAGE_DIR) if f.lower().endswith(image_extensions)]

    if not source_images:
        print(f"错误: 源目录 '{SOURCE_IMAGE_DIR}' 中没有找到任何图像文件。")
        print("请将包含人脸的原始照片放入该目录。")
        return

    print(f"--- 开始从 '{SOURCE_IMAGE_DIR}' 目录创建人脸模板 ---")
    total_faces_saved = 0

    # 遍历每一张源图片
    for image_file in source_images:
        image_path = os.path.join(SOURCE_IMAGE_DIR, image_file)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"警告: 无法读取图像 {image_file}，已跳过。")
            continue
        
        # 获取图像尺寸
        (h, w) = image.shape[:2]

        # --- 使用DNN进行人脸检测 ---
        # 构造一个blob
        blob = cv2.dnn.blobFromImage(cv2.resize(image, (300, 300)), 1.0,
                                     (300, 300), (104.0, 177.0, 123.0))
        
        # 将blob输入网络并获取检测结果
        face_detector.setInput(blob)
        detections = face_detector.forward()
        
        # 筛选出置信度最高的那个检测结果作为人脸
        best_detection_index = -1
        highest_confidence = 0
        if detections.shape[2] > 0:
            best_detection_index = np.argmax(detections[0, 0, :, 2])
            highest_confidence = detections[0, 0, best_detection_index, 2]

        print(f"\n在 '{image_file}' 中找到最可能的人脸，置信度: {highest_confidence:.2f}")

        if highest_confidence > CONFIDENCE_THRESHOLD:
            # 计算人脸边界框坐标
            box = detections[0, 0, best_detection_index, 3:7] * np.array([w, h, w, h])
            (startX, startY, endX, endY) = box.astype("int")
            
            # 确保边界框在图像范围内
            (startX, startY) = (max(0, startX), max(0, startY))
            (endX, endY) = (min(w - 1, endX), min(h - 1, endY))

            # 从原始彩色图像中裁剪出人脸区域
            face = image[startY:endY, startX:endX]
            
            if face.size == 0:
                print(f"  -> 警告: 裁剪出的人脸区域为空，跳过。")
                continue

            # 转换为灰度图并调整为标准尺寸
            gray_face = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)
            resized_face = cv2.resize(gray_face, OUTPUT_SIZE, interpolation=cv2.INTER_AREA)

            # 构建输出文件名
            person_name = os.path.splitext(image_file)[0]
            output_filename = f"{person_name}.jpg"
            output_path = os.path.join(CROPPED_TEMPLATE_DIR, output_filename)
            
            # 保存处理好的人脸模板
            cv2.imwrite(output_path, resized_face)
            print(f"  -> 已成功保存标准模板: {output_path}")
            total_faces_saved += 1
        else:
            print(f"  -> 未能在此图片中检测到符合要求的人脸 (置信度 < {CONFIDENCE_THRESHOLD})。")

    print(f"\n--- 模板创建完成 ---")
    if total_faces_saved > 0:
        print(f"总共从 {len(source_images)} 张图片中提取并保存了 {total_faces_saved} 个标准人脸模板。")
        print(f"模板已全部保存至目录: '{CROPPED_TEMPLATE_DIR}'")
    else:
        print("未生成任何模板文件。请检查源图片质量或调整检测参数。")

if __name__ == "__main__":
    try:
        create_templates_from_images()
    except IOError as e:
        print(f"\n初始化失败: {e}")
        print("这通常是由于网络问题导致无法下载所需的模型文件。")
        print("请检查您的网络连接、防火墙或代理设置，然后重试。")
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}") 