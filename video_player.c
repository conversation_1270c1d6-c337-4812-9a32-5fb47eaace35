#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <stdbool.h>
#include <fcntl.h>
#include <unistd.h>
#include <dirent.h>
#include <errno.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <linux/input.h>
#include <linux/fb.h>
#include <signal.h>

// =================================================================================
// 1. 全局配置和定义
// =================================================================================

// --- 屏幕和设备 ---
#define LCD_WIDTH 800
#define LCD_HEIGHT 480
#define LCD_BPP 4
#define LCD_SIZE (LCD_WIDTH * LCD_HEIGHT * LCD_BPP)
#define LCD_DEVICE "/dev/fb0"
#define TOUCH_DEVICE "/dev/input/event0"

// --- 路径定义 ---
#define UI_BG_PATH "/root/data/shipbofang.bmp"
#define VIDEO_DIR_PATH "/root/bmpdk"
#define FIFO_PATH "/tmp/mplayer_fifo"

// --- UI 布局 ---
#define BTN_WIDTH 64
#define BTN_HEIGHT 68
#define BTN_Y 412
#define EXIT_X 0
#define EXIT_Y 0
#define CONTROL_BAR_START_X 80

#define PROGRESS_BAR_Y 400
#define PROGRESS_BAR_H 12
#define PROGRESS_BAR_WIDTH 750
#define PROGRESS_BAR_X ((LCD_WIDTH - PROGRESS_BAR_WIDTH) / 2) // 居中对齐

// =================================================================================
// 2. 数据结构
// =================================================================================

typedef struct { char *path; } VideoFile;
typedef struct { int x, y, width, height; } Rect;

#pragma pack(1)
typedef struct {
    unsigned char signature[2];
    unsigned int fileSize;
    unsigned short reserved1;
    unsigned short reserved2;
    unsigned int dataOffset;
} BMPFileHeader;

typedef struct {
    unsigned int size;
    int width;
    int height;
    unsigned short planes;
    unsigned short bitCount;
    unsigned int compression;
    unsigned int imageSize;
    int xPixelsPerMeter;
    int yPixelsPerMeter;
    unsigned int colorsUsed;
    unsigned int colorsImportant;
} BMPInfoHeader;
#pragma pack()

// =================================================================================
// 3. 全局变量
// =================================================================================

static int g_lcd_fd = -1, g_touch_fd = -1, g_fifo_fd = -1;
static int *g_screen_buffer = NULL, *g_ui_bg_buffer = NULL;
static VideoFile *g_video_files = NULL;
static int g_video_count = 0, g_current_video_index = -1;
static pid_t g_mplayer_pid = -1;
static int g_mplayer_stdout_fd = -1;
static int g_video_progress_percent = 0;
static bool g_is_paused = true; // 初始状态为暂停，直到视频开始播放
static bool g_is_dragging_progress = false;
static bool g_was_playing_before_drag = false;

// 新增：用于精确时间计算的变量
static float g_video_total_seconds = 0.0;
static float g_video_current_seconds = 0.0;

static Rect g_rect_exit, g_rect_play, g_rect_pause, g_rect_resume, g_rect_mute;
static Rect g_rect_vol_up, g_rect_vol_down, g_rect_seek_f, g_rect_seek_b;
static Rect g_rect_prev, g_rect_next;


// =================================================================================
// 4. 函数原型
// =================================================================================

void cleanup_app(void);
int draw_bmp_to_buffer(const char *path, int* buffer, int dx, int dy);
int ensure_fifo_is_open(void);

// =================================================================================
// 5. 核心功能
// =================================================================================

bool is_touch_in_rect(int tx, int ty, const Rect* r) {
    return (tx>=r->x && tx<=(r->x+r->width) && ty>=r->y && ty<=(r->y+r->height));
}

void transform_touch_coords(int *x, int *y) {
    if (x) *x = *x * LCD_WIDTH / 800;
    if (y) *y = *y * LCD_HEIGHT / 480;
}

void send_mplayer_cmd(const char* cmd) {
    if (ensure_fifo_is_open() == -1) {
        printf("[CMD] FIFO not available. Cannot send command: %s", cmd);
        return;
    }
    printf("[CMD] Sending command to mplayer: %s", cmd);
    if (write(g_fifo_fd, cmd, strlen(cmd)) == -1) {
        // 修复：区分管道已满 (EAGAIN) 和其他错误
        if (errno == EAGAIN) {
             printf("[CMD] MPlayer FIFO is full, command not sent. MPlayer may be busy.\n");
        } else {
             perror("[CMD] write to fifo failed, closing pipe");
             close(g_fifo_fd);
             g_fifo_fd = -1; // 标记为已关闭，以便下次尝试重开
        }
    }
}

void stop_video() {
    if (g_mplayer_pid > 0) {
        kill(g_mplayer_pid, SIGKILL); // 确保mplayer被终止
        waitpid(g_mplayer_pid, NULL, 0);
        g_mplayer_pid = -1;
    }
    if (g_mplayer_stdout_fd != -1) { close(g_mplayer_stdout_fd); g_mplayer_stdout_fd = -1; }
    if (g_fifo_fd != -1) { close(g_fifo_fd); g_fifo_fd = -1; }
    g_video_progress_percent = 0;
    g_is_paused = true; // 视频停止后，回到暂停状态
    // 重置时间变量
    g_video_total_seconds = 0.0;
    g_video_current_seconds = 0.0;
}

void play_video(int index) {
    if (index < 0 || index >= g_video_count) return;
    stop_video();
    g_current_video_index = index;
    g_video_progress_percent = 0;
    g_is_paused = false; // 新视频开始播放，状态为"非暂停"
    
    // 播放新视频前，先完整绘制一次UI背景和空的进度条
    render_ui();
    display_on_lcd();

    mkfifo(FIFO_PATH, 0666);
    int p_fds[2];
    if (pipe(p_fds) == -1) { perror("pipe"); return; }

    g_mplayer_pid = fork();
    if (g_mplayer_pid == 0) { // Child
        close(p_fds[0]);
        dup2(p_fds[1], STDOUT_FILENO);
        int null_fd = open("/dev/null", O_WRONLY);
        if (null_fd != -1) dup2(null_fd, STDERR_FILENO);
        
        // 恢复为与 chumuo.c 相似的 mplayer 启动逻辑
        execlp("mplayer", "mplayer", "-slave", "-quiet",
               "-input", "file="FIFO_PATH,
               "-vo", "fbdev2:/dev/fb0", "-vf", "scale=800:400",
               g_video_files[g_current_video_index].path, NULL);
        perror("execlp mplayer"); exit(1);
    } else if (g_mplayer_pid > 0) { // Parent
        close(p_fds[1]);
        g_mplayer_stdout_fd = p_fds[0];
        fcntl(g_mplayer_stdout_fd, F_SETFL, O_NONBLOCK);

        // --- 修复: 使用更健壮的方式打开FIFO ---
        if (g_fifo_fd != -1) { close(g_fifo_fd); g_fifo_fd = -1; }
        // 第一次打开管道
        ensure_fifo_is_open();

    } else { // Error
        perror("fork");
        close(p_fds[0]); close(p_fds[1]);
    }
}

// 新增: 一个确保FIFO管道被打开的健壮函数
int ensure_fifo_is_open(void) {
    // 如果管道已经是打开的，直接返回
    if (g_fifo_fd != -1) {
        return g_fifo_fd;
    }
    
    // 如果mplayer没在运行，则无法打开
    if (g_mplayer_pid <= 0) {
        return -1;
    }

    // 管道已关闭或从未打开，尝试打开它
    // 循环尝试打开，给mplayer一点时间来创建管道
    int attempts = 0;
    while ((g_fifo_fd = open(FIFO_PATH, O_WRONLY | O_NONBLOCK)) == -1) {
        if (attempts >= 10) { // 尝试1秒
            perror("[FIFO] Error: Could not open FIFO for writing after multiple attempts");
            g_fifo_fd = -1;
            return -1;
        }
        usleep(100000); // 等待100ms
        attempts++;
    }
    
    // 关键修复: 保持管道为非阻塞模式，移除 fcntl 调用
    // int flags = fcntl(g_fifo_fd, F_GETFL, 0);
    // fcntl(g_fifo_fd, F_SETFL, flags & ~O_NONBLOCK);
    printf("[FIFO] FIFO pipe opened successfully for writing in non-blocking mode.\n");
    return g_fifo_fd;
}

// =================================================================================
// 6. UI渲染
// =================================================================================

// 新增: 一个能恢复整个底部UI的函数，用于mplayer启动后
void redraw_bottom_ui_on_screen(int percent) {
    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) {
        perror("mmap for bottom ui failed");
        return;
    }
    int *screen_ptr = (int *)fbp;

    // 1. 从内存缓冲区恢复整个底部UI的背景 (y=400到y=479)
    int offset = LCD_WIDTH * 400;
    memcpy(screen_ptr + offset, g_ui_bg_buffer + offset, LCD_WIDTH * 80 * sizeof(int));

    // 2. 在恢复的背景上绘制进度条
    percent = (percent < 0) ? 0 : (percent > 100 ? 100 : percent);
    int filled_width = PROGRESS_BAR_WIDTH * percent / 100;
    for (int y = PROGRESS_BAR_Y; y < PROGRESS_BAR_Y + PROGRESS_BAR_H; y++) {
        for (int x = PROGRESS_BAR_X; x < PROGRESS_BAR_X + PROGRESS_BAR_WIDTH; x++) {
            screen_ptr[y * LCD_WIDTH + x] = (x < (PROGRESS_BAR_X + filled_width)) ? 0x003399FF : 0x00404040;
        }
    }
    
    munmap(fbp, LCD_SIZE);
}

// 新增: 一个高效的、只在屏幕上更新进度条的函数
void update_progress_bar_on_screen(int percent) {
    percent = (percent < 0) ? 0 : (percent > 100 ? 100 : percent);
    int filled_width = PROGRESS_BAR_WIDTH * percent / 100;

    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) {
        perror("mmap for progress bar failed");
        return;
    }

    int *screen_ptr = (int *)fbp;

    // 只重绘750像素宽的居中区域
    for (int y = PROGRESS_BAR_Y; y < PROGRESS_BAR_Y + PROGRESS_BAR_H; y++) {
        for (int x = PROGRESS_BAR_X; x < PROGRESS_BAR_X + PROGRESS_BAR_WIDTH; x++) {
            screen_ptr[y * LCD_WIDTH + x] = (x < (PROGRESS_BAR_X + filled_width)) ? 0x003399FF : 0x00404040;
        }
    }

    munmap(fbp, LCD_SIZE);
}

void render_progress_bar(int percent) {
    percent = (percent < 0) ? 0 : (percent > 100 ? 100 : percent);
    int filled_width = PROGRESS_BAR_WIDTH * percent / 100;
    
    // 绘制750像素宽的居中进度条
    for (int y = PROGRESS_BAR_Y; y < PROGRESS_BAR_Y + PROGRESS_BAR_H; y++) {
        for (int x = PROGRESS_BAR_X; x < PROGRESS_BAR_X + PROGRESS_BAR_WIDTH; x++) {
            g_screen_buffer[y * LCD_WIDTH + x] = (x < (PROGRESS_BAR_X + filled_width)) ? 0x003399FF : 0x00404040;
        }
    }
}

void render_ui() {
    memcpy(g_screen_buffer, g_ui_bg_buffer, LCD_SIZE);
    if(g_mplayer_pid > 0) { render_progress_bar(g_video_progress_percent); }
}

void display_on_lcd() {
    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp != MAP_FAILED) {
        memcpy(fbp, g_screen_buffer, LCD_SIZE);
        munmap(fbp, LCD_SIZE);
    }
}

// =================================================================================
// 7. 事件与初始化
// =================================================================================

void handle_touch(int x, int y) {
    if (is_touch_in_rect(x, y, &g_rect_exit)) { raise(SIGINT); }
    else if (is_touch_in_rect(x, y, &g_rect_play)) {
        if(g_current_video_index == -1 && g_video_count > 0) {
            play_video(0); // 开始播放第一个视频
        } else if (g_is_paused) {
            send_mplayer_cmd("pause\n"); // 使用基础的pause指令
            g_is_paused = false;
        }
    } 
    else if (is_touch_in_rect(x, y, &g_rect_pause)) {
        if (!g_is_paused) {
            send_mplayer_cmd("pause\n"); // 使用基础的pause指令
            g_is_paused = true;
        }
    }
    else if (is_touch_in_rect(x, y, &g_rect_resume)) {
        if (g_is_paused) {
            send_mplayer_cmd("pause\n"); // 使用基础的pause指令
            g_is_paused = false;
        }
    }
    else if (is_touch_in_rect(x, y, &g_rect_mute)) { send_mplayer_cmd("mute\n"); }
    else if (is_touch_in_rect(x, y, &g_rect_vol_up)) { send_mplayer_cmd("volume +10\n"); }
    else if (is_touch_in_rect(x, y, &g_rect_vol_down)) { send_mplayer_cmd("volume -10\n"); }
    else if (is_touch_in_rect(x, y, &g_rect_seek_f)) { send_mplayer_cmd("seek +10 0\n"); }
    else if (is_touch_in_rect(x, y, &g_rect_seek_b)) { send_mplayer_cmd("seek -10 0\n"); }
    else if (is_touch_in_rect(x, y, &g_rect_prev)) {
        if(g_video_count>0) play_video((g_current_video_index-1+g_video_count)%g_video_count);
    } else if (is_touch_in_rect(x, y, &g_rect_next)) {
        if(g_video_count>0) play_video((g_current_video_index+1)%g_video_count);
    }
}

void sig_handler(int sig) { cleanup_app(); exit(0); }

void cleanup_app() {
    printf("Cleaning up video player...\n");
    stop_video();
    unlink(FIFO_PATH);
    if (g_screen_buffer) free(g_screen_buffer);
    if (g_ui_bg_buffer) free(g_ui_bg_buffer);
    if (g_lcd_fd != -1) close(g_lcd_fd);
    if (g_touch_fd != -1) close(g_touch_fd);
    if (g_video_files) {
        for (int i = 0; i < g_video_count; i++) free(g_video_files[i].path);
        free(g_video_files);
    }
    printf("Video player cleaned up.\n");
}

int initialize_app(void) {
    g_lcd_fd = open(LCD_DEVICE, O_RDWR);
    g_touch_fd = open(TOUCH_DEVICE, O_RDONLY);
    if(g_lcd_fd == -1 || g_touch_fd == -1) { perror("device open"); return -1; }

    g_screen_buffer = malloc(LCD_SIZE);
    g_ui_bg_buffer = malloc(LCD_SIZE);
    if(!g_screen_buffer || !g_ui_bg_buffer) { perror("malloc"); return -1; }
    
    if (draw_bmp_to_buffer(UI_BG_PATH, g_ui_bg_buffer, 0, 0) != 0) {
        memset(g_ui_bg_buffer, 0, LCD_SIZE);
    }

    int x = CONTROL_BAR_START_X, y = BTN_Y;
    g_rect_exit   = (Rect){EXIT_X, EXIT_Y, BTN_WIDTH, BTN_HEIGHT};
    g_rect_play   = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_pause  = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_resume = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_mute   = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_vol_up = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_vol_down= (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_seek_f = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_seek_b = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_prev   = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT}; x += BTN_WIDTH;
    g_rect_next   = (Rect){x, y, BTN_WIDTH, BTN_HEIGHT};

    signal(SIGINT, sig_handler); signal(SIGTERM, sig_handler);
    
    DIR *dir = opendir(VIDEO_DIR_PATH);
    if(!dir) { perror("opendir"); return 0; }
    struct dirent *entry;
    while((entry = readdir(dir)) != NULL) {
        if (strcasestr(entry->d_name, ".avi")) {
            g_video_count++;
            g_video_files = realloc(g_video_files, g_video_count * sizeof(VideoFile));
            char* path = malloc(strlen(VIDEO_DIR_PATH) + strlen(entry->d_name) + 2);
            sprintf(path, "%s/%s", VIDEO_DIR_PATH, entry->d_name);
            g_video_files[g_video_count-1].path = path;
        }
    }
    closedir(dir);
    printf("Found %d video files.\n", g_video_count);
    return 0;
}

// =================================================================================
// 8. Main Loop
// =================================================================================

int main(void) {
    if (initialize_app() != 0) { cleanup_app(); return -1; }
    atexit(cleanup_app);

    render_ui();
    display_on_lcd();
    // if (g_video_count > 0) { play_video(0); } // 确保此行被注释或删除

    struct input_event ev;
    int touch_x=0, touch_y=0;

    while(1) {
        fd_set fds; FD_ZERO(&fds); FD_SET(g_touch_fd, &fds);
        int max_fd = g_touch_fd;
        if (g_mplayer_stdout_fd != -1) {
            FD_SET(g_mplayer_stdout_fd, &fds);
            if (g_mplayer_stdout_fd > max_fd) max_fd = g_mplayer_stdout_fd;
        }

        struct timeval tv;
        struct timeval *timeout_ptr = NULL; // 默认是无限等待（即阻塞）

        // 只有在视频正在播放时，我们才设置超时来轮询进度
        if (!g_is_paused) {
            tv.tv_sec = 0;
            tv.tv_usec = 500000; // 0.5秒
            timeout_ptr = &tv;
        }
        
        int ret = select(max_fd + 1, &fds, NULL, NULL, timeout_ptr);

        if (ret < 0 && errno != EINTR) { perror("select"); break; }
        
        // 仅在超时（即播放期间）才发送获取时间的指令
        if (ret == 0) { 
            send_mplayer_cmd("get_time_pos\n");
            if (g_video_total_seconds < 0.1) {
                 send_mplayer_cmd("get_time_length\n");
            }
            continue; 
        }
        
        if (g_mplayer_stdout_fd != -1 && FD_ISSET(g_mplayer_stdout_fd, &fds)) {
            char buf[512]; // 增加缓冲区以处理多行输出
            int n = read(g_mplayer_stdout_fd, buf, sizeof(buf)-1);
            if (n > 0) {
                buf[n] = '\0';
                char *line;
                char *saveptr;
                
                // 使用 strtok_r 逐行处理 mplayer 的输出
                line = strtok_r(buf, "\n", &saveptr);
                while (line != NULL) {
                    // 检查是否为总时长信息
                    if (strncmp(line, "ANS_LENGTH=", 11) == 0) {
                        float new_total_seconds = atof(line + 11);
                        // 当第一次获取到有效的总时长时，重绘整个底部UI
                        if (g_video_total_seconds < 0.1 && new_total_seconds > 0.1) {
                            g_video_total_seconds = new_total_seconds;
                            printf("[INFO] Video length received: %.2f seconds\n", g_video_total_seconds);
                            redraw_bottom_ui_on_screen(0); // 恢复UI并绘制0%进度条
                        }
                    }
                    
                    // 检查是否为当前时间点信息
                    if (strncmp(line, "ANS_TIME_POSITION=", 18) == 0) {
                        g_video_current_seconds = atof(line + 18);
                        if (g_video_total_seconds > 0.1) { // 确保总时长有效
                            int new_p = (int)((g_video_current_seconds / g_video_total_seconds) * 100);
                            if (new_p > 100) new_p = 100;
                            if (new_p != g_video_progress_percent) {
                                g_video_progress_percent = new_p;
                                update_progress_bar_on_screen(g_video_progress_percent);
                            }
                        }
                    }
                    line = strtok_r(NULL, "\n", &saveptr);
                }
            } else if (n == 0){ // Pipe closed by mplayer
                // 视频结束后，先画满进度条，再处理后续逻辑
                if (g_video_progress_percent < 100) {
                    update_progress_bar_on_screen(100);
                    usleep(100000); // 短暂显示满进度
                }

                stop_video();
                if (g_current_video_index + 1 < g_video_count) {
                    play_video(g_current_video_index + 1);
                } else {
                    g_current_video_index = -1;
                    render_ui();
                    display_on_lcd();
                }
            }
        }
        
        if (FD_ISSET(g_touch_fd, &fds)) {
            if (read(g_touch_fd, &ev, sizeof(ev)) == sizeof(ev)) {
                // 总是先更新坐标
                if (ev.type == EV_ABS) {
                    if (ev.code == ABS_X) touch_x = ev.value;
                    if (ev.code == ABS_Y) touch_y = ev.value;
                }

                // 处理按下事件
                if (ev.type == EV_KEY && ev.code == BTN_TOUCH && ev.value == 1) {
                    int tx = touch_x, ty = touch_y;
                    transform_touch_coords(&tx, &ty);
                    Rect pb_rect = {PROGRESS_BAR_X, PROGRESS_BAR_Y, PROGRESS_BAR_WIDTH, PROGRESS_BAR_H};

                    if (is_touch_in_rect(tx, ty, &pb_rect)) {
                        g_is_dragging_progress = true;
                        g_was_playing_before_drag = !g_is_paused;
                        
                        // 为了更好的拖拽体验，先暂停视频
                        if (g_was_playing_before_drag) {
                            send_mplayer_cmd("pause\n");
                            g_is_paused = true;
                        }
                    }
                }
                
                // 处理拖动事件
                if (g_is_dragging_progress && ev.type == EV_ABS && ev.code == ABS_X) {
                    int tx = touch_x; // 只关心X轴
                    transform_touch_coords(&tx, NULL); // Y轴坐标无关，可以传NULL

                    int new_percent = (tx - PROGRESS_BAR_X) * 100 / PROGRESS_BAR_WIDTH;
                    if (new_percent < 0) new_percent = 0;
                    if (new_percent > 100) new_percent = 100;

                    if (new_percent != g_video_progress_percent) {
                        // 关键修复: 拖动时只更新UI，不发送指令
                        g_video_progress_percent = new_percent;
                        update_progress_bar_on_screen(g_video_progress_percent);
                    }
                }

                // 处理抬起事件
                if (ev.type == EV_KEY && ev.code == BTN_TOUCH && ev.value == 0) {
                    if (g_is_dragging_progress) {
                        g_is_dragging_progress = false;

                        // 关键修复: 只在手指抬起时发送一次seek指令
                        char cmd[64];
                        sprintf(cmd, "seek %d 1\n", g_video_progress_percent);
                        send_mplayer_cmd(cmd);

                        if (g_was_playing_before_drag) {
                            send_mplayer_cmd("pause\n"); // 恢复播放
                            g_is_paused = false;
                        }
                    } else {
                        // 如果不是拖拽，那就是普通的按钮点击
                        int final_x = touch_x, final_y = touch_y;
                        transform_touch_coords(&final_x, &final_y);
                        handle_touch(final_x, final_y);
                    }
                }
            }
        }
    }
    return 0;
}


// =================================================================================
// 9. BMP Drawing Function
// =================================================================================
int draw_bmp_to_buffer(const char *path, int* buffer, int dx, int dy) {
    FILE *f = fopen(path, "rb");
    if (!f) { perror("fopen bmp"); return -1; }

    BMPFileHeader fh; BMPInfoHeader fih;
    fread(&fh, sizeof(fh), 1, f);
    if (fh.signature[0] != 'B' || fh.signature[1] != 'M') { fclose(f); return -1; }
    fread(&fih, sizeof(fih), 1, f);
    if (fih.bitCount != 24 || fih.compression != 0) { fclose(f); return -1; }

    int w = fih.width, h = abs(fih.height);
    int row_size = (w * 3 + 3) & ~3;
    unsigned char *data = malloc(row_size);
    if (!data) { fclose(f); return -1; }
    
    fseek(f, fh.dataOffset, SEEK_SET);
    for (int y = 0; y < h; y++) {
        fread(data, 1, row_size, f);
        for (int x = 0; x < w; x++) {
            int sx = dx + x, sy = dy + ((fih.height > 0) ? (h - 1 - y) : y);
            if(sx < 0 || sx >= LCD_WIDTH || sy < 0 || sy >= LCD_HEIGHT) continue;
            buffer[sy * LCD_WIDTH + sx] = (data[x*3+2] << 16) | (data[x*3+1] << 8) | data[x*3];
        }
    }
    free(data); fclose(f);
    return 0;
} 