#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <stdbool.h>
#include <fcntl.h>
#include <unistd.h>
#include <dirent.h>
#include <errno.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <linux/input.h>
#include <linux/fb.h>
#include <linux/rtc.h> //  : RTC头文件
#include <math.h>
#include <time.h>
#include <signal.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

// =================================================================================
//  : 实体按键配置
// =================================================================================
#define KEY2_GPIO 28 // 播放/暂停, 静音 (GPIOA28 -> 0*32+28=28)
#define KEY3_GPIO 62 // 音量减, 上一曲 (GPIOB30 -> 1*32+30=62)
#define KEY4_GPIO 63 // 音量加, 下一曲 (GPIOB31 -> 1*32+31=63)
#defin  e KEY6_GPIO 41 // 返回, 主页 (GPIOB9 -> 1*32+9=41)
#define NUM_KEYS 4
#define LONG_PRESS_DURATION_US 1000000 // 1秒 (1,000,000 微秒)
#define DEBOUNCE_DURATION_US 20000     // 20毫秒 (20,000 微秒) 防抖时间

// =================================================================================
// 1. 全局配置和定义
// =================================================================================

// --- 红外接近检测定义 ( ) ---
#define INFRARED_DEVICE "/dev/lirc0"
#define INFRARED_SILENCE_THRESHOLD_S 4.0  // 如果超过4.0秒没收到任何信号，就认为人已离开
#define INFRARED_DETECT_THRESHOLD_S 3.0   // 需要持续检测到活动3秒钟才触发警报

// --- 屏幕和基本路径定义 ---
#define LCD_WIDTH 800
#define LCD_HEIGHT 480
#define LCD_BPP 4
#define LCD_SIZE (LCD_WIDTH * LCD_HEIGHT * LCD_BPP)

#define LCD_DEVICE "/dev/fb0"
#define TOUCH_DEVICE "/dev/input/event0"

#define BACKGROUND_BMP_PATH "/root/data/zhujiemian.bmp"
#define WSD_BG_PATH "/root/data/wsd.bmp"
#define HUIHUA_BG_PATH "/root/data/huihua.bmp"
#define BEEP_UI_BG_PATH "/root/data/fmq.bmp"
#define LED_UI_BG_PATH "/root/data/LED.bmp"
#define AUDIO_BG_PATH "/root/data/music.bmp"
#define AUDIO_DIR_PATH "/data"
#define AUDIO_FIFO_PATH "/tmp/audio_fifo"
#define KT_BG_PATH "/root/data/kt.bmp" //  ：空调背景图
#define STARTUP_VIDEO_PATH "/data/kjdh.avi" //  ：开机动画视频路径

#define COMMAND_FIFO_PATH "/tmp/media_fifo" // 统一的媒体控制管道

#define VIDEO_FILE_PATH "/data/music.avi"
#define AUDIO_COMMAND_FIFO_PATH "/tmp/audio_fifo" // 重命名
#define VIDEO_COMMAND_FIFO_PATH "/tmp/video_fifo" //  


#define PROGRESS_BAR_Y 400
#define PROGRESS_BAR_H 12
#define PROGRESS_BAR_WIDTH 750
#define PROGRESS_BAR_X ((LCD_WIDTH - PROGRESS_BAR_WIDTH) / 2)

#define GEC6818_GET_DHTDATA _IOR('K', 0, unsigned int)// 生成读操作的ioctl命令码
#define TEXT_COLOR 0x00000000 // 黑色
#define DOUBLE_CLICK_TIME_US 500000 // 500ms

// --- 蜂鸣器控制命令 ---
#define BEEP_DEVICE "/dev/beep"
#define PWM_CONTROL_DEVICE "/sys/devices/platform/pwm/pwm.2"
#define BEEP_ON  0
#define BEEP_OFF 1
#define PWM_PERIOD 1000000   // 
#define PWM_DUTY_STEP 100000 // 10% 音量步进

// ---  ：背光控制定义 ---
#define BACKLIGHT_PATH "/sys/class/backlight/pwm-backlight"

// ---  ：背光控制定义 (最终方案: 完全手动控制) ---
#define BACKLIGHT_PWM_DEVICE "/sys/devices/platform/pwm/pwm.0"
#define BACKLIGHT_PWM_PERIOD 100000 // 周期 (100,000 ns -> 10 kHz)

// --- LED 控制命令 (参考 led_control.c 的正确实现) ---
#define LED_DEVICE "/dev/Led"
#define LED_MAGIC 'x'
// ioctl 命令只负责选择哪个LED
#define GEC_LED1    _IO(LED_MAGIC, 0)
#define GEC_LED2    _IO(LED_MAGIC, 1)
#define GEC_LED3    _IO(LED_MAGIC, 2)
#define GEC_LED4    _IO(LED_MAGIC, 3)
// 第三个参数负责传递 '开' 或 '关'
#define LED_CMD_ON  0
#define LED_CMD_OFF 1

// --- 主界面布局定义 ---
#define BTN_WIDTH 293
#define BTN_HEIGHT 80
#define H_SPACING 31
#define V_SPACING 21
#define START_X 74
#define START_Y 42

// ---  ：人脸识别相关定义 ---
#define VM_IP "*************"
#define VM_PORT 54322
#define PHOTO_KEY_DEVICE "/dev/input/event5" //  ：新增：拍照按键设备
#define TEMP_BMP_PATH "/tmp/face_rec.bmp"
#define RECOGNITION_RESULT_PATH "/root/result.txt"
#define RECOGNITION_LISTEN_PORT 54323 //  ：新增：用于接收成功信号的端口
#define PICTURE_SEND_INTERVAL_MS 200  //  ：修改：发送间隔从秒改为毫秒，提高发送速率

// 应用程序状态枚举：定义应用程序可能处于的所有界面状态，用于状态切换和界面渲染控制
typedef enum {
    STATE_MAIN_MENU,        // 主菜单状态：应用启动后的默认界面，包含各功能入口按钮
    STATE_SENSOR_VIEW,      // 传感器视图状态：显示温湿度等传感器数据的界面
    STATE_PAINT_VIEW,       // 绘图视图状态：提供绘图功能的界面，支持多种绘图工具
    STATE_BEEPER_VIEW,      // 蜂鸣器视图状态：用于控制蜂鸣器开关及音量的界面
    STATE_LED_VIEW,         // LED视图状态：用于控制LED开关及流水灯效果的界面
    STATE_AUDIO_PLAYER,     // 音频播放器状态：音频文件播放界面，支持播放、暂停、切歌等操作
    STATE_AC_VIEW,          //  ：空调界面状态：模拟空调控制的界面
    STATE_CAMERA_VIEW,      //  ：新增：摄像头查看状态
    STATE_EXIT              // 退出状态：标记应用程序即将退出
} AppState;

// 绘图工具枚举：定义绘图功能中可用的工具类型，用于切换不同的绘图模式
typedef enum {
    TOOL_PENCIL,    // 铅笔工具：自由绘制任意线条
    TOOL_CIRCLE,    // 圆形工具：绘制圆形
    TOOL_RECT,      // 矩形工具：绘制矩形
    TOOL_LINE,      // 直线工具：绘制直线
    TOOL_ERASER     // 橡皮擦工具：擦除已绘制的内容
} PaintTool;

// ---  ：按键状态机定义 ---
typedef enum {
    KEY_STATE_IDLE,             // 按键空闲（已释放）
    KEY_STATE_DEBOUNCING_PRESS, // 按下防抖中
    KEY_STATE_PRESSED,          // 按键确认按下
    KEY_STATE_DEBOUNCING_RELEASE// 释放防抖中
} KeyState;


// =================================================================================
// 2. 数据结构
// =================================================================================

// --- 矩形区域 ---
typedef struct {
    int x, y, width, height;
} Rect;

// --- 音频文件 ---
typedef struct {
    char *path;
    float duration; //  ：音频时长（秒）
} AudioFile;

// --- BMP文件头 (需要1字节对齐) ---
#pragma pack(1)
typedef struct {
    unsigned char signature[2];
    unsigned int fileSize;
    unsigned short reserved1;
    unsigned short reserved2;
    unsigned int dataOffset;
} BMPFileHeader;

typedef struct {
    unsigned int size;
    int width;
    int height;
    unsigned short planes;
    unsigned short bitCount;
    unsigned int compression;
    unsigned int imageSize;
    int xPixelsPerMeter;
    int yPixelsPerMeter;
    unsigned int colorsUsed;
    unsigned int colorsImportant;
} BMPInfoHeader;
#pragma pack()

// --- 字体数据 ---
static const unsigned char font8x8_basic[128][8] = {
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0000 (nul) */
  {0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00, 0x18, 0x00},   /* U+0001 */
  {0x66, 0x66, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0002 */
  {0x66, 0x66, 0x7E, 0x66, 0x7E, 0x66, 0x66, 0x00},   /* U+0003 */
  {0x18, 0x3C, 0x60, 0x3C, 0x0C, 0x3C, 0x18, 0x00},   /* U+0004 */
  {0x60, 0x66, 0x0C, 0x18, 0x30, 0x66, 0x06, 0x00},   /* U+0005 */
  {0x3C, 0x66, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00},   /* U+0006 */
  {0x00, 0x18, 0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00},   /* U+0007 */
  {0xFF, 0xE7, 0xE7, 0xFF, 0xE7, 0xE7, 0xFF, 0x00},   /* U+0008 */
  {0x18, 0x3C, 0x66, 0x66, 0x3C, 0x18, 0x18, 0x00},   /* U+0009 */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+000A */
  {0x1C, 0x36, 0x63, 0x41, 0x41, 0x63, 0x36, 0x1C},   /* U+000B */
  {0x0C, 0x18, 0x30, 0x60, 0x60, 0x30, 0x18, 0x0C},   /* U+000C */
  {0x18, 0x00, 0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00},   /* U+000D */
  {0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x18, 0x30, 0x00},   /* U+000E */
  {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00},   /* U+000F */
  {0x00, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x80, 0x00},   /* U+0010 */
  {0x00, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x00, 0x00},   /* U+0011 */
  {0x00, 0x18, 0x3C, 0x66, 0x66, 0x3C, 0x18, 0x00},   /* U+0012 */
  {0x00, 0x3C, 0x18, 0x3C, 0x18, 0x3C, 0x18, 0x00},   /* U+0013 */
  {0x18, 0x3C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00},   /* U+0014 */
  {0x18, 0x18, 0x18, 0x18, 0x3C, 0x18, 0x3C, 0x00},   /* U+0015 */
  {0x3C, 0x66, 0x6E, 0x7E, 0x60, 0x60, 0x00, 0x00},   /* U+0016 */
  {0x18, 0x18, 0x30, 0x30, 0x60, 0x60, 0x00, 0x00},   /* U+0017 */
  {0x00, 0x60, 0x30, 0x18, 0x0C, 0x60, 0x00, 0x00},   /* U+0018 */
  {0x00, 0x0C, 0x18, 0x30, 0x60, 0x0C, 0x00, 0x00},   /* U+0019 */
  {0x00, 0x00, 0x00, 0x7E, 0x00, 0x18, 0x3C, 0x00},   /* U+001A */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+001B */
  {0x00, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},   /* U+001C */
  {0x00, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, 0x18},   /* U+001D */
  {0x00, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x00},   /* U+001E */
  {0x00, 0xC0, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x00},   /* U+001F */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0020 (space) */
  {0x18, 0x18, 0x18, 0x18, 0x18, 0x00, 0x18, 0x00},   /* U+0021 (!) */
  {0x66, 0x66, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0022 (") */
  {0x66, 0x66, 0x7E, 0x66, 0x7E, 0x66, 0x66, 0x00},   /* U+0023 (#) */
  {0x18, 0x3C, 0x60, 0x3C, 0x0C, 0x3C, 0x18, 0x00},   /* U+0024 ($) */
  {0x00, 0xC6, 0xC6, 0x6C, 0x18, 0x36, 0x6C, 0x00},   /* U+0025 (%) */
  {0x3C, 0x66, 0x3C, 0x76, 0x66, 0x66, 0x3C, 0x00},   /* U+0026 (&) */
  {0x18, 0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0027 (') */
  {0x0C, 0x18, 0x30, 0x30, 0x30, 0x18, 0x0C, 0x00},   /* U+0028 (() */
  {0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x18, 0x30, 0x00},   /* U+0029 ()) */
  {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00},   /* U+002A (*) */
  {0x00, 0x18, 0x18, 0x7E, 0x18, 0x18, 0x00, 0x00},   /* U+002B (+) */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x0C},   /* U+002C (,) */
  {0x00, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x00, 0x00},   /* U+002D (-) */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00},   /* U+002E (.) */
  {0x00, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC0, 0x00},   /* U+002F (/) */
  {0x3C, 0x66, 0x6E, 0x76, 0x66, 0x66, 0x3C, 0x00},   /* U+0030 (0) */
  {0x18, 0x38, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00},   /* U+0031 (1) */
  {0x3C, 0x66, 0x06, 0x1C, 0x30, 0x66, 0x7E, 0x00},   /* U+0032 (2) */
  {0x3C, 0x66, 0x06, 0x3C, 0x06, 0x66, 0x3C, 0x00},   /* U+0033 (3) */
  {0x0E, 0x1E, 0x36, 0x66, 0x7E, 0x06, 0x0E, 0x00},   /* U+0034 (4) */
  {0x7E, 0x60, 0x7C, 0x06, 0x06, 0x66, 0x3C, 0x00},   /* U+0035 (5) */
  {0x3C, 0x60, 0x60, 0x7C, 0x66, 0x66, 0x3C, 0x00},   /* U+0036 (6) */
  {0x7E, 0x66, 0x06, 0x0C, 0x18, 0x18, 0x18, 0x00},   /* U+0037 (7) */
  {0x3C, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x3C, 0x00},   /* U+0038 (8) */
  {0x3C, 0x66, 0x66, 0x3E, 0x06, 0x0C, 0x38, 0x00},   /* U+0039 (9) */
  {0x00, 0x18, 0x18, 0x00, 0x00, 0x18, 0x18, 0x00},   /* U+003A (:) */
  {0x00, 0x18, 0x18, 0x00, 0x00, 0x18, 0x18, 0x0C},   /* U+003B (;) */
  {0x0C, 0x18, 0x30, 0x60, 0x30, 0x18, 0x0C, 0x00},   /* U+003C (<) */
  {0x00, 0x00, 0x7E, 0x00, 0x7E, 0x00, 0x00, 0x00},   /* U+003D (=) */
  {0x30, 0x18, 0x0C, 0x06, 0x0C, 0x18, 0x30, 0x00},   /* U+003E (>) */
  {0x3C, 0x66, 0x06, 0x0C, 0x18, 0x00, 0x18, 0x00},   /* U+003F (?) */
  {0x3C, 0x66, 0x66, 0x76, 0x7E, 0x76, 0x3B, 0x00},   /* U+0040 (@) */
  {0x18, 0x3C, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x00},   /* U+0041 (A) */
  {0x7C, 0x66, 0x66, 0x7C, 0x66, 0x66, 0x7C, 0x00},   /* U+0042 (B) */
  {0x3C, 0x66, 0x60, 0x60, 0x60, 0x66, 0x3C, 0x00},   /* U+0043 (C) */
  {0x78, 0x6C, 0x66, 0x66, 0x66, 0x6C, 0x78, 0x00},   /* U+0044 (D) */
  {0x7E, 0x60, 0x60, 0x7C, 0x60, 0x60, 0x7E, 0x00},   /* U+0045 (E) */
  {0x7E, 0x60, 0x60, 0x7C, 0x60, 0x60, 0x60, 0x00},   /* U+0046 (F) */
  {0x3C, 0x66, 0x60, 0x6E, 0x66, 0x66, 0x3C, 0x00},   /* U+0047 (G) */
  {0x66, 0x66, 0x66, 0x7E, 0x66, 0x66, 0x66, 0x00},   /* U+0048 (H) */
  {0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00},   /* U+0049 (I) */
  {0x0E, 0x06, 0x06, 0x06, 0x06, 0x66, 0x3C, 0x00},   /* U+004A (J) */
  {0x66, 0x6C, 0x78, 0x70, 0x78, 0x6C, 0x66, 0x00},   /* U+004B (K) */
  {0x60, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E, 0x00},   /* U+004C (L) */
  {0xC6, 0xEE, 0xFE, 0xD6, 0xC6, 0xC6, 0xC6, 0x00},   /* U+004D (M) */
  {0x66, 0xE6, 0xF6, 0xDE, 0xCE, 0x66, 0x66, 0x00},   /* U+004E (N) */
  {0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x00},   /* U+004F (O) */
  {0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60, 0x60, 0x00},   /* U+0050 (P) */
  {0x3C, 0x66, 0x66, 0x66, 0x66, 0x6E, 0x3C, 0x0C},   /* U+0051 (Q) */
  {0x7C, 0x66, 0x66, 0x7C, 0x6C, 0x66, 0x66, 0x00},   /* U+0052 (R) */
  {0x3C, 0x66, 0x60, 0x3C, 0x06, 0x66, 0x3C, 0x00},   /* U+0053 (S) */
  {0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00},   /* U+0054 (T) */
  {0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x00},   /* U+0055 (U) */
  {0x66, 0x66, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00},   /* U+0056 (V) */
  {0xC6, 0xC6, 0xC6, 0xD6, 0xFE, 0xEE, 0xC6, 0x00},   /* U+0057 (W) */
  {0x66, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x66, 0x00},   /* U+0058 (X) */
  {0x66, 0x66, 0x66, 0x3C, 0x18, 0x18, 0x18, 0x00},   /* U+0059 (Y) */
  {0x7E, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x7E, 0x00},   /* U+005A (Z) */
  {0x7E, 0x60, 0x60, 0x60, 0x60, 0x60, 0x7E, 0x00},   /* U+005B ([) */
  {0xC0, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x00},   /* U+005C (\) */
  {0x7E, 0x06, 0x06, 0x06, 0x06, 0x06, 0x7E, 0x00},   /* U+005D (]) */
  {0x18, 0x3C, 0x66, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+005E (^) */
  {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF},   /* U+005F (_) */
  {0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+0060 (`) */
  {0x00, 0x00, 0x3C, 0x06, 0x3E, 0x66, 0x3E, 0x00},   /* U+0061 (a) */
  {0x60, 0x60, 0x7C, 0x66, 0x66, 0x66, 0x7C, 0x00},   /* U+0062 (b) */
  {0x00, 0x00, 0x3C, 0x66, 0x60, 0x66, 0x3C, 0x00},   /* U+0063 (c) */
  {0x06, 0x06, 0x3E, 0x66, 0x66, 0x66, 0x3E, 0x00},   /* U+0064 (d) */
  {0x00, 0x00, 0x3C, 0x66, 0x7C, 0x60, 0x3C, 0x00},   /* U+0065 (e) */
  {0x1C, 0x36, 0x30, 0x7C, 0x30, 0x30, 0x30, 0x00},   /* U+0066 (f) */
  {0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x7C},   /* U+0067 (g) */
  {0x60, 0x60, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x00},   /* U+0068 (h) */
  {0x18, 0x00, 0x38, 0x18, 0x18, 0x18, 0x3C, 0x00},   /* U+0069 (i) */
  {0x0C, 0x00, 0x1C, 0x0C, 0x0C, 0x0C, 0xCC, 0x78},   /* U+006A (j) */
  {0x60, 0x60, 0x6C, 0x78, 0x70, 0x78, 0x6C, 0x00},   /* U+006B (k) */
  {0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x3C, 0x00},   /* U+006C (l) */
  {0x00, 0x00, 0xCC, 0xFE, 0xD6, 0xD6, 0xC6, 0x00},   /* U+006D (m) */
  {0x00, 0x00, 0x7C, 0x66, 0x66, 0x66, 0x66, 0x00},   /* U+006E (n) */
  {0x00, 0x00, 0x3C, 0x66, 0x66, 0x66, 0x3C, 0x00},   /* U+006F (o) */
  {0x00, 0x00, 0x7C, 0x66, 0x66, 0x7C, 0x60, 0x60},   /* U+0070 (p) */
  {0x00, 0x00, 0x3E, 0x66, 0x66, 0x3E, 0x06, 0x0E},   /* U+0071 (q) */
  {0x00, 0x00, 0x7C, 0x66, 0x60, 0x60, 0x60, 0x00},   /* U+0072 (r) */
  {0x00, 0x00, 0x3E, 0x60, 0x3C, 0x06, 0x7C, 0x00},   /* U+0073 (s) */
  {0x30, 0x30, 0x7E, 0x30, 0x30, 0x36, 0x1C, 0x00},   /* U+0074 (t) */
  {0x00, 0x00, 0x66, 0x66, 0x66, 0x66, 0x3E, 0x00},   /* U+0075 (u) */
  {0x00, 0x00, 0x66, 0x66, 0x66, 0x3C, 0x18, 0x00},   /* U+0076 (v) */
  {0x00, 0x00, 0xC6, 0xC6, 0xD6, 0xFE, 0x6C, 0x00},   /* U+0077 (w) */
  {0x00, 0x00, 0x66, 0x3C, 0x18, 0x3C, 0x66, 0x00},   /* U+0078 (x) */
  {0x00, 0x00, 0x66, 0x66, 0x66, 0x3E, 0x06, 0x7C},   /* U+0079 (y) */
  {0x00, 0x00, 0x7E, 0x0C, 0x18, 0x30, 0x7E, 0x00},   /* U+007A (z) */
  {0x0E, 0x18, 0x18, 0x70, 0x18, 0x18, 0x0E, 0x00},   /* U+007B ({) */
  {0x18, 0x18, 0x18, 0x00, 0x18, 0x18, 0x18, 0x00},   /* U+007C (|) */
  {0x70, 0x18, 0x18, 0x0E, 0x18, 0x18, 0x70, 0x00},   /* U+007D (}) */
  {0x76, 0xDC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   /* U+007E (~) */
  {0x1C, 0x36, 0x63, 0x6B, 0x6B, 0x03, 0x00, 0x00},   /* U+007F */
};

// =================================================================================
// 4. 全局变量
// =================================================================================

// --- 系统资源 ---
static AppState g_app_state = STATE_MAIN_MENU;
static int g_lcd_fd = -1;
static int g_touch_fd = -1;
static int g_beep_fd = -1;
static int g_led_fd = -1;
static int g_rtc_fd = -1; //  : RTC设备文件句柄
static int g_infrared_fd = -1; //  ：红外设备文件句柄
static int g_photo_key_fd = -1; //  ：新增：拍照按键文件句柄
static int *g_screen_buffer = NULL; 
static int *g_paint_canvas_buffer = NULL;
static int *g_audio_bg_buffer = NULL; //  : 用于音频播放器背景的独立缓冲区
static int *g_main_menu_bg_buffer = NULL; //  : 用于主菜单背景的独立缓冲区

// --- 音频播放状态 ---
static AudioFile *g_audio_files = NULL;
static int g_audio_count = 0;
static int g_current_audio_index = -1;
static pid_t g_audio_mplayer_pid = -1; // 修改：明确为音频进程ID
static pid_t g_video_mplayer_pid = -1; //  ：视频进程ID
static int g_command_fifo_fd = -1;
static int g_mplayer_stdout_pipe_fd = -1;

static int g_progress_percent = 0;
static float g_total_seconds = 0.0;
static float g_current_seconds = 0.0;
static bool g_is_paused = true;
static struct timeval g_playback_start_time; //  ：用于手动计算播放时间
static bool g_audio_paused_for_external_app = false; //  ：标记是否为外部应用暂停了音频

// --- 绘图状态变量 ---
static PaintTool g_current_tool = TOOL_PENCIL;
static int g_color_palette[] = { 
    0x00000000, 0x00FF0000, 0x0000FF00, 0x000000FF, 0x00FFFF00, // 黑, 红, 绿, 蓝, 黄
    0x00800080, 0x00FFA500, 0x0000FFFF, 0x00FF00FF, 0x00A52A2A  // 紫, 橙, 青, 洋红, 棕
};
#define NUM_COLORS (sizeof(g_color_palette) / sizeof(int))
static int g_current_color_index = 0;
static int g_draw_color = 0x00000000; // 默认为黑色
static bool g_is_drawing = false;
static bool g_is_selecting_color = false; //  ：是否正在选择颜色
static bool g_is_confirming_exit = false; //  ：是否在确认退出
static int g_start_x, g_start_y, g_last_x, g_last_y;

// --- 主界面按钮区域 ---
static Rect g_rect_album, g_rect_video, g_rect_led, g_rect_beep;
static Rect g_rect_audio, g_rect_sensor, g_rect_paint, g_rect_ac;
static Rect g_rect_camera_entry; //  ：新增：进入摄像头界面的按钮
static Rect g_rect_voice_client_trigger; // 新增：启动语音识别客户端的按钮
static Rect g_rect_back_from_sensor;

// --- 蜂鸣器界面按钮区域 ---
static Rect g_rect_back_from_beeper;
static Rect g_rect_beep_on, g_rect_beep_off;
static Rect g_rect_beep_vol_up, g_rect_beep_vol_down;

// --- LED界面按钮区域 ---
static Rect g_rect_led_on, g_rect_led_off, g_rect_led_marquee;
static Rect g_rect_back_from_led;

// --- 音频播放器按钮区域 ---
static Rect g_rect_back_from_audio;
static Rect g_rect_audio_play, g_rect_audio_pause, g_rect_audio_resume;
static Rect g_rect_audio_mute, g_rect_audio_vol_up, g_rect_audio_vol_down;
static Rect g_rect_audio_seek_f, g_rect_audio_seek_b, g_rect_audio_prev, g_rect_audio_next;

// --- 绘图界面按钮区域 ---
static Rect g_rect_tool_pencil, g_rect_tool_circle, g_rect_tool_rect, g_rect_tool_line, g_rect_tool_eraser;
static Rect g_rect_back_from_paint;
static Rect g_rect_color_blocks[NUM_COLORS]; //  ：调色板色块区域
static Rect g_rect_confirm_dialog; //  ：确认对话框区域
static Rect g_rect_confirm_yes, g_rect_confirm_no; //  ：YES/NO 按钮区域

// --- 蜂鸣器音量状态 ---
// ---  ：为了让音量变化更明显，使用非线性的音量等级 ---
static const int g_volume_levels[] = {
    0,      // 0: 静音
    5000,   // 1: 
    20000,  // 2:
    80000,  // 3:
    250000, // 4: 
    500000  // 5: 
};
#define NUM_VOLUME_LEVELS (sizeof(g_volume_levels) / sizeof(int))
static int g_current_volume_level = 4; // 默认音量等级为 4 (较大)

// --- LED 流水灯状态 ---
static bool g_is_marquee_running = false;
static int g_marquee_led_index = 0;

// ---  ：红外检测状态变量 ---
static struct timeval g_last_infrared_activity_time = {0, 0};
static struct timeval g_infrared_continuous_start_time = {0, 0};
static bool g_is_person_present = false; // 替换 g_is_infrared_continuously_active
static bool g_infrared_alarm_triggered = false;

// --- 空调界面按钮区域 ( ) ---
static Rect g_rect_back_from_ac;
static Rect g_rect_ac_on, g_rect_ac_off;

// ---  ：实体按键状态变量 ---
static int g_key_fds[NUM_KEYS] = {-1, -1, -1, -1};
static const int g_key_gpios[NUM_KEYS] = {KEY2_GPIO, KEY3_GPIO, KEY4_GPIO, KEY6_GPIO};
static char g_key_initial_state[NUM_KEYS]; //  : 用于存储每个按键的初始电平
static KeyState g_key_state[NUM_KEYS]; // 修改: 使用状态机替代布尔值
static bool g_key_long_press_triggered[NUM_KEYS] = {false, false, false, false};
static struct timeval g_key_press_start_time[NUM_KEYS];
static struct timeval g_key_debounce_start_time[NUM_KEYS]; //  : 防抖计时器

// ---  : 外部应用进程ID ---
static pid_t g_external_app_pid = -1;
static pid_t g_camera_view_pid = -1; //  ：新增：摄像头查看界面的mplayer进程ID

// ---  : 周期性任务计时器 ---
static struct timeval g_last_sensor_refresh_time = {0, 0};
static struct timeval g_last_marquee_update_time = {0, 0};
static struct timeval g_last_time_update_time = {0, 0}; //  : 时间显示刷新计时器

// ---  : 主界面亮度控制按钮区域 ---
static Rect g_rect_brightness_down, g_rect_brightness_up;

// ---  ：背光亮度状态 (最终方案: 完全手动控制) ---
static const int g_brightness_levels[] = { 10000, 20000, 30000, 50000, 70000, 90000, 99999 }; // 7个亮度等级, 修正：最大值应小于周期
#define NUM_BRIGHTNESS_LEVELS (sizeof(g_brightness_levels) / sizeof(int))
static int g_current_brightness_level = 4; // 默认亮度等级 (索引 0-6)

//  ：新增全局变量，用于新的背光控制逻辑
static bool g_use_standard_backlight_interface = false; // 是否使用标准 /sys/class/backlight 接口
static int g_max_brightness = 0; // 从标准接口读取的最大亮度值

// ---  ：新增：摄像头界面按钮区域 ---
static Rect g_rect_back_from_camera;

// 修改全局变量定义，添加摄像头界面专用的语音按钮区域
static Rect g_rect_voice_client_trigger; // 新增：启动语音识别客户端的按钮
static Rect g_rect_voice_client_trigger_camera; // 新增：摄像头界面专用的语音识别按钮
static Rect g_rect_voice_client_trigger_subviews; // 新增：其他子界面专用的语音识别按钮


// =================================================================================
// 5. 函数原型声明
// =================================================================================

// --- 渲染函数 ---
void render_main_menu(void);
void render_sensor_view(void);
void render_paint_view(void);
void render_beeper_view(void);
void render_led_view(void);
void render_audio_player_view(void);
void render_ac_view(void); //  
void render_camera_view(void); //  ：新增
void render_color_palette(void); //  
void display_on_lcd(void);
void update_time_display(void); //  : 更新时间显示
int draw_bmp_to_buffer(const char *bmp_path, int* buffer, int dx, int dy);
void draw_text(int* buffer, int x, int y, const char *text, int color);
void draw_line(int* buffer, int x0, int y0, int x1, int y1, int color);
void draw_line_ex(int* buffer, int x0, int y0, int x1, int y1, int size, int color);
void draw_circle(int* buffer, int xc, int yc, int r, int color);
void draw_thick_circle(int* buffer, int xc, int yc, int r, int color);
void draw_voice_button(int* buffer); // 新增：绘制语音识别按钮的函数

// --- 视频和进度条渲染函数 ( ) ---
void render_progress_bar(void);
void update_progress_bar_on_screen(int percent);
void redraw_bottom_ui_on_screen(void);

// --- 事件处理 ---
void handle_touch_main_menu(int x, int y);
void handle_touch_sensor_view(int x, int y);
void handle_touch_paint_view(struct input_event *ev);
void handle_touch_beeper_view(int x, int y);
void handle_touch_led_view(int x, int y);
void handle_touch_audio_player(int x, int y);
void handle_touch_ac_view(int x, int y); //  
void handle_touch_camera_view(int x, int y); //  ：新增

// ---  ：文件保存 ---
int save_canvas_to_bmp(const char* directory_path);
void render_confirm_dialog(void);
static void handle_camera_snapshot(void); //  ：新增
static void ensure_directory_exists(const char* path); //  ：新增

// --- 初始化和清理 ---
int initialize_app(void);
void cleanup_app(void);

//  ：设置PWM参数的辅助函数
void set_pwm_params(const char* device_path, int period, int duty_cycle);
void set_backlight_level(int level); //  

// --- 音视频播放控制函数 (重命名/ ) ---
void stop_media(void);
void play_media(int index);
void send_media_cmd(const char* cmd);
float get_audio_duration(const char* path); //  
void start_video(void);
void start_audio(int index);
void stop_video(void);

// ---  : 按键处理函数 ---
static int initialize_keys(void);
static void poll_physical_keys(void);
static void handle_key_action(int key_index, bool is_long_press);

// ---  ：人脸识别功能函数 ---
static void send_image_to_vm(const char* filepath);
static int capture_screen_to_bmp(const char* filepath);
static bool check_local_recognition_result(void);
static void face_recognition_loop(void);

// 新增：语音命令处理函数原型
static void check_for_voice_command(void);
static void handle_voice_command(const char* command);


// =================================================================================
// 6. 核心绘图和工具函数
// =================================================================================

//  : 用于在缓冲区中查找特定字节序列（签名）的辅助函数
static bool find_signature(const char* buffer, int buffer_len, const char* sig, int sig_len) {
    if (sig_len <= 0 || buffer_len < sig_len) {
        return false;
    }
    for (int i = 0; i <= buffer_len - sig_len; ++i) {
        if (memcmp(buffer + i, sig, sig_len) == 0) {
            return true; // 找到了
        }
    }
    return false; // 未找到
}

// 将像素数据从内存缓冲区刷到LCD屏幕
void display_on_lcd() {
    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) {
        perror("mmap for display failed");
        return;
    }
    memcpy(fbp, g_screen_buffer, LCD_SIZE);
    munmap(fbp, LCD_SIZE);
}

// 检测触摸点是否在指定矩形内
bool is_touch_in_rect(int tx, int ty, const Rect* rect) {
    return (tx >= rect->x && tx <= (rect->x + rect->width) &&
            ty >= rect->y && ty <= (rect->y + rect->height));
}

// 将24位BMP文件内容绘制到内存缓冲区指定位置
int draw_bmp_to_buffer(const char *bmp_path, int* buffer, int dx, int dy) {
    FILE *bmp_file = fopen(bmp_path, "rb");
    if (bmp_file == NULL) {
        fprintf(stderr, "Cannot open BMP: %s\n", bmp_path);
        return -1;
    }

    BMPFileHeader file_header;
    fread(&file_header, sizeof(BMPFileHeader), 1, bmp_file);
    if (file_header.signature[0] != 'B' || file_header.signature[1] != 'M') {
        fprintf(stderr, "Not a BMP file: %s\n", bmp_path);
        fclose(bmp_file);
        return -1;
    }

    BMPInfoHeader info_header;
    fread(&info_header, sizeof(BMPInfoHeader), 1, bmp_file);
    if (info_header.bitCount != 24 || info_header.compression != 0) {
        fprintf(stderr, "Unsupported BMP format (must be 24-bit uncompressed): %s\n", bmp_path);
        fclose(bmp_file);
        return -1;
    }

    int width = info_header.width;
    int height = abs(info_header.height);
    int row_size = ((width * 24 + 31) / 32) * 4;
    int image_size = row_size * height;
    
    unsigned char *bmp_data = (unsigned char *)malloc(image_size);
    if (bmp_data == NULL) {
        perror("malloc for bmp data failed");
        fclose(bmp_file);
        return -1;
    }

    fseek(bmp_file, file_header.dataOffset, SEEK_SET);
    fread(bmp_data, 1, image_size, bmp_file);
    fclose(bmp_file);
    
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int screen_x = dx + x;
            int screen_y = dy + y;

            if (screen_x < 0 || screen_x >= LCD_WIDTH || screen_y < 0 || screen_y >= LCD_HEIGHT) {
                continue;
            }

            int bmp_y = (info_header.height > 0) ? (height - 1 - y) : y;
            int bmp_index = bmp_y * row_size + x * 3;
            unsigned char b = bmp_data[bmp_index];
            unsigned char g = bmp_data[bmp_index + 1];
            unsigned char r = bmp_data[bmp_index + 2];
            
            buffer[screen_y * LCD_WIDTH + screen_x] = (r << 16) | (g << 8) | b;
        }
    }

    free(bmp_data);
    return 0;
}

// 在屏幕缓冲区指定位置绘制一个8x8字符
static void draw_char(int* buffer, int x, int y, char c, int color) {
    if (c < 0 || c > 127) return; 

    const unsigned char* glyph = font8x8_basic[(int)c];
    for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
            if ((glyph[i] >> (7 - j)) & 1) {
                int screen_x = x + j;
                int screen_y = y + i;
                if (screen_x >= 0 && screen_x < LCD_WIDTH && screen_y >= 0 && screen_y < LCD_HEIGHT) {
                    buffer[screen_y * LCD_WIDTH + screen_x] = color;
                }
            }
        }
    }
}

// 在屏幕缓冲区指定位置绘制字符串
void draw_text(int* buffer, int x, int y, const char *text, int color) {
    for (int i = 0; text[i] != '\0'; i++) {
        draw_char(buffer, x + i * 8, y, text[i], color);
    }
}

// 触摸坐标转换
void transform_touch_coords(int *raw_x, int *raw_y) {
    // 假设触摸屏分辨率为800x480，映射到800x480的LCD
    *raw_x = *raw_x * LCD_WIDTH / 800;
    *raw_y = *raw_y * LCD_HEIGHT / 480;
}

//  ：获取音频文件时长
float get_audio_duration(const char* path) {
    char cmd[1024];
    // 使用-really-quiet代替-quiet，-frames 0确保它只读头信息不播放
    sprintf(cmd, "mplayer -vo null -ao null -really-quiet -frames 0 -identify \"%s\"", path);
    
    FILE* pipe = popen(cmd, "r");
    if (!pipe) {
        perror("popen for mplayer identify failed");
        return 0.0f;
    }
    
    char buffer[256];
    float duration = 0.0f;
    
    while (fgets(buffer, sizeof(buffer), pipe) != NULL) {
        if (strncmp(buffer, "ID_LENGTH=", 10) == 0) {
            duration = atof(buffer + 10);
            break; // 找到后就退出
        }
    }
    
    pclose(pipe);
    printf("Identified duration for %s: %.2f seconds\n", path, duration);
    return duration;
}

// =================================================================================
// 7. 界面渲染函数
// =================================================================================

//  更新并显示时间
void update_time_display(void) {
    if (g_rtc_fd == -1) return;

    struct rtc_time rtc_tm;
    if (ioctl(g_rtc_fd, RTC_RD_TIME, &rtc_tm) == -1) {
        perror("ioctl(RTC_RD_TIME) failed");
        return;
    }

    // ---  : 简化的时区校正逻辑 ---
    // 之前的 timegm/localtime 方案因为目标系统缺少时区文件而失败。
    // 这里我们使用一个更简单、更健壮的方法：硬编码时区偏移量。
    // 这对于像中国这样没有夏令时的固定时区来说是完全可行的。
    // 注意：这个简化版没有处理日期变更（例如UTC 18:00 会显示为 02:00，但日期不会+1）。
    // 这在嵌入式应用中通常是可以接受的折衷方案。
    const int TIMEZONE_OFFSET_HOURS = 8; // 中国时区为 UTC+8
    rtc_tm.tm_hour = (rtc_tm.tm_hour + TIMEZONE_OFFSET_HOURS) % 24;
    // --- 逻辑结束 ---

    char time_str[9]; // "HH:MM:SS\0"
    sprintf(time_str, "%02d:%02d:%02d", rtc_tm.tm_hour, rtc_tm.tm_min, rtc_tm.tm_sec);

    // 已移除命令行时间显示

    int text_width = 8 * 8; // 8个字符 * 8像素/字符
    int x = (LCD_WIDTH - text_width) / 2;
    int y = 10; // 顶部边距
    
    // 关键: 为了防止闪烁, 先从干净的背景缓冲区恢复时间区域
    if (g_main_menu_bg_buffer) {
        for (int i = y; i < y + 8; i++) { // 字体高度为8
            // 复制一行
            memcpy(g_screen_buffer + i * LCD_WIDTH + x, 
                   g_main_menu_bg_buffer + i * LCD_WIDTH + x, 
                   text_width * sizeof(int));
        }
    }

    // 在恢复的背景上绘制新时间
    draw_text(g_screen_buffer, x, y, time_str, 0x00FFFFFF); // 白色
}

// 渲染主菜单
void render_main_menu() {
    // 1. 从内存缓冲区绘制背景图片
    if (g_main_menu_bg_buffer) {
        memcpy(g_screen_buffer, g_main_menu_bg_buffer, LCD_SIZE);
    } else {
        // 如果背景缓冲区加载失败，则填充纯色
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x00112233;
    }

    // 2. 渲染初始时间
    update_time_display();
}

// 渲染温湿度传感器界面
void render_sensor_view() {
    // 1. 绘制背景
    if (draw_bmp_to_buffer(WSD_BG_PATH, g_screen_buffer, 0, 0) != 0) {
        // 背景加载失败则填充灰色
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x00CCCCCC;
    }

    // 2. 获取并显示温湿度数据
    int dht_fd = open("/dev/dht11_dev", O_RDWR);
    if (dht_fd != -1) {
        unsigned char dht_data[2]; // data[0] -> Temp, data[1] -> Humi
        if (ioctl(dht_fd, GEC6818_GET_DHTDATA, dht_data) == 0) {
            char data_str[4]; 
            
            // 显示温度
            sprintf(data_str, "%d", dht_data[0]);
            draw_text(g_screen_buffer, 157, 136, data_str, TEXT_COLOR);

            // 显示湿度
            sprintf(data_str, "%d", dht_data[1]);
            draw_text(g_screen_buffer, 157, 311, data_str, TEXT_COLOR);
        } else {
            draw_text(g_screen_buffer, 157, 136, "ERR", TEXT_COLOR);
            draw_text(g_screen_buffer, 157, 311, "ERR", TEXT_COLOR);
        }
        close(dht_fd);
    } else {
        draw_text(g_screen_buffer, 157, 136, "N/A", TEXT_COLOR);
        draw_text(g_screen_buffer, 157, 311, "N/A", TEXT_COLOR);
    }
    
    // 3. 绘制语音识别按钮（右上角）
    // 注意：draw_voice_button函数现在不再绘制可见的按钮，仅保留功能区域
    // 这里只是为了保持代码结构的一致性
}

// 渲染绘图界面
void render_paint_view(void) {
    // 1. 绘制背景到画布和屏幕
    if (draw_bmp_to_buffer(HUIHUA_BG_PATH, g_paint_canvas_buffer, 0, 0) != 0) {
        // 背景加载失败则填充白色作为画布
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_paint_canvas_buffer[i] = 0x00FFFFFF;
    }
    // 将初始化后的画布内容复制到主屏幕缓冲区
    memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);

    // 2. 可以在这里绘制工具栏的图标或边框，此处暂时留空
    // 例如：draw_bmp_to_buffer("/root/data/pencil_icon.bmp", g_screen_buffer, g_rect_tool_pencil.x, g_rect_tool_pencil.y);
}

//  ：渲染调色板（直接绘制在g_screen_buffer上）
void render_color_palette(void) {
    for (int i = 0; i < NUM_COLORS; i++) {
        Rect r = g_rect_color_blocks[i];
        int color = g_color_palette[i];
        for (int y = r.y; y < r.y + r.height; y++) {
            for (int x = r.x; x < r.x + r.width; x++) {
                if (x >= 0 && x < LCD_WIDTH && y >= 0 && y < LCD_HEIGHT) {
                    g_screen_buffer[y * LCD_WIDTH + x] = color;
                }
            }
        }
    }
}

//  ：渲染确认对话框
void render_confirm_dialog(void) {
    // 1. 绘制一个半透明的覆盖层来使背景变暗
    for (int y = 0; y < LCD_HEIGHT; y++) {
        for (int x = 0; x < LCD_WIDTH; x++) {
            int original_color = g_screen_buffer[y * LCD_WIDTH + x];
            int r = (original_color >> 16) & 0xFF;
            int g = (original_color >> 8) & 0xFF;
            int b = original_color & 0xFF;
            g_screen_buffer[y * LCD_WIDTH + x] = ((r >> 1) << 16) | ((g >> 1) << 8) | (b >> 1);
        }
    }

    // 2. 绘制对话框主体
    Rect r = g_rect_confirm_dialog;
    int box_color = 0x00EEEEEE;
    int border_color = 0x00333333;
    for (int y = r.y; y < r.y + r.height; y++) {
        for (int x = r.x; x < r.x + r.width; x++) {
            if (x == r.x || x == r.x + r.width - 1 || y == r.y || y == r.y + r.height - 1) {
                g_screen_buffer[y * LCD_WIDTH + x] = border_color;
            } else {
                g_screen_buffer[y * LCD_WIDTH + x] = box_color;
            }
        }
    }

    // 3. 绘制文本
    const char* msg = "Save canvas?";
    int text_width = strlen(msg) * 8;
    draw_text(g_screen_buffer, r.x + (r.width - text_width) / 2, r.y + 30, msg, 0x00000000);

    // 4. 绘制按钮
    Rect btn_y = g_rect_confirm_yes;
    Rect btn_n = g_rect_confirm_no;
    for(int y=btn_y.y; y<btn_y.y+btn_y.height; y++)
        for(int x=btn_y.x; x<btn_y.x+btn_y.width; x++)
             g_screen_buffer[y*LCD_WIDTH+x] = 0x00CCCCCC;
    draw_text(g_screen_buffer, btn_y.x + 22, btn_y.y + 12, "YES", 0x00000000);

    for(int y=btn_n.y; y<btn_n.y+btn_n.height; y++)
        for(int x=btn_n.x; x<btn_n.x+btn_n.width; x++)
             g_screen_buffer[y*LCD_WIDTH+x] = 0x00CCCCCC;
    draw_text(g_screen_buffer, btn_n.x + 26, btn_n.y + 12, "NO", 0x00000000);
}

// 渲染蜂鸣器界面
void render_beeper_view(void) {
    if (draw_bmp_to_buffer(BEEP_UI_BG_PATH, g_screen_buffer, 0, 0) != 0) {
        // 背景加载失败则填充灰色
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x00CCCCCC;
    }
    
    // 语音识别按钮在右上角，不再绘制可见的按钮，仅保留功能区域
}

// 渲染LED控制界面
void render_led_view(void) {
    if (draw_bmp_to_buffer(LED_UI_BG_PATH, g_screen_buffer, 0, 0) != 0) {
        // 背景加载失败则填充深灰色
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x00333333;
    }
    
    // 语音识别按钮在右上角，不再绘制可见的按钮，仅保留功能区域
    // draw_voice_button(g_screen_buffer);
}

// 渲染音频播放器界面
void render_audio_player_view(void) {
    if (!g_audio_bg_buffer) {
        // 背景缓冲区未初始化，绘制纯黑
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x00000000;
        return;
    }
    // 将干净的背景从缓冲区复制到屏幕缓冲区
    memcpy(g_screen_buffer, g_audio_bg_buffer, LCD_SIZE);
    
    // 在背景之上绘制进度条
    render_progress_bar();
    
    // 语音识别按钮在右上角，不再绘制可见的按钮，仅保留功能区域
    // draw_voice_button(g_screen_buffer);
}

//  绘制进度条到屏幕缓冲区
void render_progress_bar(void) {
    int percent = g_progress_percent;
    percent = (percent < 0) ? 0 : (percent > 100 ? 100 : percent);//修正
    int filled_width = PROGRESS_BAR_WIDTH * percent / 100;
    
    for (int y = PROGRESS_BAR_Y; y < PROGRESS_BAR_Y + PROGRESS_BAR_H; y++) {
        for (int x = PROGRESS_BAR_X; x < PROGRESS_BAR_X + PROGRESS_BAR_WIDTH; x++) {
            g_screen_buffer[y * LCD_WIDTH + x] = (x < (PROGRESS_BAR_X + filled_width)) ? 0x003399FF : 0x00404040;
        }//PROGRESS_BAR_X 和 PROGRESS_BAR_Y 是进度条左上角的坐标。PROGRESS_BAR_H 是进度条的高度（像素）。
    }
}

//  : 直接在屏幕上更新进度条 (mplayer播放时用)
void update_progress_bar_on_screen(int percent) {
    percent = (percent < 0) ? 0 : (percent > 100 ? 100 : percent);
    int filled_width = PROGRESS_BAR_WIDTH * percent / 100;

    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) return;

    int *screen_ptr = (int *)fbp;
    for (int y = PROGRESS_BAR_Y; y < PROGRESS_BAR_Y + PROGRESS_BAR_H; y++) {
        for (int x = PROGRESS_BAR_X; x < PROGRESS_BAR_X + PROGRESS_BAR_WIDTH; x++) {
            screen_ptr[y * LCD_WIDTH + x] = (x < (PROGRESS_BAR_X + filled_width)) ? 0x003399FF : 0x00404040;
        }
    }
    munmap(fbp, LCD_SIZE);
}

//  : mplayer启动后，恢复被覆盖的底部UI
void redraw_bottom_ui_on_screen(void) {
    void *fbp = mmap(NULL, LCD_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, g_lcd_fd, 0);
    if (fbp == MAP_FAILED) return;
    int *screen_ptr = (int *)fbp;

    // 1. 从内存缓冲区恢复整个底部UI的背景 (y=400到y=479)
    int offset = LCD_WIDTH * 400;
    if (g_audio_bg_buffer) {
         memcpy(screen_ptr + offset, g_audio_bg_buffer + offset, LCD_WIDTH * 80 * sizeof(int));
    }
    
    // 2. 在恢复的背景上绘制进度条
    update_progress_bar_on_screen(g_progress_percent);
    
    munmap(fbp, LCD_SIZE);
}

//  ：渲染空调界面
void render_ac_view(void) {
    if (draw_bmp_to_buffer(KT_BG_PATH, g_screen_buffer, 0, 0) != 0) {
        // 背景加载失败则填充浅蓝色
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_screen_buffer[i] = 0x00ADD8E6;
    }
    
    // 语音识别按钮在右上角，不再绘制可见的按钮，仅保留功能区域
    // draw_voice_button(g_screen_buffer);
}

//  ：新增：渲染摄像头预览界面
void render_camera_view(void) {
    // 启动mplayer以显示摄像头画面
    if (g_camera_view_pid <= 0) {
        g_camera_view_pid = fork();
        if (g_camera_view_pid == 0) { // 子进程
            execlp("mplayer", "mplayer", "tv://",
                   "-tv", "driver=v4l2:device=/dev/video7:width=800:height=480:fps=15",
                   "-vo", "fbdev2:/dev/fb0", "-vf", "scale=800:480,mirror",
                   "-nosound", "-quiet", NULL);
            perror("execlp 启动摄像头 mplayer 失败 (camera view)");
            exit(1);
        } else if (g_camera_view_pid < 0) {
            perror("fork 摄像头 mplayer 失败 (camera view)");
        }
    }
    // 注意：这里不绘制任何东西，因为mplayer会直接渲染到framebuffer上
    // 但我们可以绘制一个UI叠加层，例如返回按钮
    // 为了简单起见，我们暂时不画，但返回区域是有效的。
    
    // 摄像头界面不需要绘制语音按钮，保持界面干净
    // 语音按钮功能仍然保留在右上角，但不可见
}

// =================================================================================
// 8. 事件处理函数
// =================================================================================

void handle_touch_main_menu(int x, int y) {
    if (g_external_app_pid != -1) {
        printf("正在运行外部应用，忽略主菜单触摸事件。\n");
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
    } else if (is_touch_in_rect(x, y, &g_rect_album)) {
        printf("Album button clicked! Launching album_app...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork failed");
        } else if (pid == 0) { // 子进程
            execlp("./album_app", "album_app", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp album_app failed");
            exit(1); 
        } else { // 父进程
            // 修改: 不再使用阻塞的 wait(NULL)，而是记录PID，让主循环去处理
            g_external_app_pid = pid;
        }

    } else if (is_touch_in_rect(x, y, &g_rect_video)) {
        printf("Video button clicked! Launching video_app...\n");
        
        //  逻辑：如果音频正在播放，则暂停它
        if (g_audio_mplayer_pid > 0 && !g_is_paused) {
            printf("Pausing background audio for video player.\n");
            send_media_cmd("pause\n");
            g_audio_paused_for_external_app = true;
        }

        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for video_app failed");
            // 如果fork失败，需要恢复音频
            if (g_audio_paused_for_external_app) {
                printf("Fork failed, resuming background audio.\n");
                send_media_cmd("pause\n");
                g_audio_paused_for_external_app = false;
            }
        } else if (pid == 0) { // 子进程
            execlp("./video_app", "video_app", NULL);
            perror("execlp video_app failed");
            exit(1); 
        } else { // 父进程
            // 修改: 不再使用阻塞的 wait(NULL)，而是记录PID
            g_external_app_pid = pid;
        }

    } else if (is_touch_in_rect(x, y, &g_rect_led)) {
        printf("LED button clicked! Switching to LED view...\n");
        g_app_state = STATE_LED_VIEW;
    } else if (is_touch_in_rect(x, y, &g_rect_beep)) {
        printf("Beep button clicked! Switching to beeper view...\n");
        g_app_state = STATE_BEEPER_VIEW;
    } else if (is_touch_in_rect(x, y, &g_rect_audio)) {
        printf("Audio button clicked! Switching to audio player...\n");
        g_app_state = STATE_AUDIO_PLAYER;
        start_video(); // 进入界面时，启动视频
        // 如果当前没有任何音乐在播放，则自动播放第一首
        if (g_audio_mplayer_pid <= 0 && g_audio_count > 0) {
            start_audio(0);
        }
    } else if (is_touch_in_rect(x, y, &g_rect_sensor)) {
        printf("Sensor button clicked! Switching to sensor view...\n");
        g_app_state = STATE_SENSOR_VIEW;
    } else if (is_touch_in_rect(x, y, &g_rect_paint)) {
        printf("Paint button clicked! Switching to paint view...\n");
        g_app_state = STATE_PAINT_VIEW;
    } else if (is_touch_in_rect(x, y, &g_rect_ac)) {
        printf("A/C button clicked! Switching to A/C view...\n");
        g_app_state = STATE_AC_VIEW;
    } else if (is_touch_in_rect(x, y, &g_rect_brightness_down)) {
        printf("Brightness Down clicked.\n");
        if (g_current_brightness_level > 0) {
            g_current_brightness_level--;
            set_backlight_level(g_current_brightness_level);
        }
    } else if (is_touch_in_rect(x, y, &g_rect_brightness_up)) {
        printf("Brightness Up clicked.\n");
        if (g_current_brightness_level < NUM_BRIGHTNESS_LEVELS - 1) {
            g_current_brightness_level++;
            set_backlight_level(g_current_brightness_level);
        }
    } else if (is_touch_in_rect(x, y, &g_rect_camera_entry)) {
        printf("Camera button clicked! Switching to camera view...\n");
        g_app_state = STATE_CAMERA_VIEW;
    }
}

// 处理温湿度界面的触摸事件
void handle_touch_sensor_view(int x, int y) {
    // 检查是否点击了语音识别按钮（右上角）
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger_subviews)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_back_from_sensor)) {
        g_app_state = STATE_MAIN_MENU;
    }
}

// 处理蜂鸣器界面的触摸事件
void handle_touch_beeper_view(int x, int y) {
    // 检查是否点击了语音识别按钮（右上角）
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger_subviews)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_back_from_beeper)) {
        // 返回前，确保蜂鸣器已关闭
        if (g_beep_fd != -1) {
            set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
            ioctl(g_beep_fd, BEEP_OFF, 1);
        }
        g_app_state = STATE_MAIN_MENU;
        return;
    }

    if (g_beep_fd == -1) {
        printf("Beeper device not available.\n");
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_beep_on)) {
        printf("Beep ON clicked.\n");
        ioctl(g_beep_fd, BEEP_ON, 1);
        // 如果当前是静音，则自动设置为一个中等音量
        if (g_current_volume_level == 0) {
            g_current_volume_level = 3; // 设为中等音量
        }
        int current_duty_cycle = g_volume_levels[g_current_volume_level];
        set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, current_duty_cycle);

    } else if (is_touch_in_rect(x, y, &g_rect_beep_off)) {
        printf("Beep OFF clicked.\n");
        set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
        ioctl(g_beep_fd, BEEP_OFF, 1);

    } else if (is_touch_in_rect(x, y, &g_rect_beep_vol_up)) {
        if (g_current_volume_level < NUM_VOLUME_LEVELS - 1) {
            g_current_volume_level++;
        }
        int new_duty_cycle = g_volume_levels[g_current_volume_level];
        printf("Volume Up. Level: %d/%d, Duty Cycle: %d (%.1f%%)\n", g_current_volume_level, NUM_VOLUME_LEVELS - 1, new_duty_cycle, (float)new_duty_cycle * 100 / PWM_PERIOD);
        ioctl(g_beep_fd, BEEP_ON, 1);
        set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, new_duty_cycle);

    } else if (is_touch_in_rect(x, y, &g_rect_beep_vol_down)) {
        if (g_current_volume_level > 0) {
            g_current_volume_level--;
        }
        int new_duty_cycle = g_volume_levels[g_current_volume_level];
        printf("Volume Down. Level: %d/%d, Duty Cycle: %d (%.1f%%)\n", g_current_volume_level, NUM_VOLUME_LEVELS - 1, new_duty_cycle, (float)new_duty_cycle * 100 / PWM_PERIOD);

        if (new_duty_cycle == 0) {
            // 音量为0时，彻底关闭
            set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
            ioctl(g_beep_fd, BEEP_OFF, 1);
        } else {
            ioctl(g_beep_fd, BEEP_ON, 1);
            set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, new_duty_cycle);
        }
    }
}

// 处理LED界面的触摸事件
void handle_touch_led_view(int x, int y) {
    // 检查是否点击了语音识别按钮（右上角）
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger_subviews)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_back_from_led)) {
        g_is_marquee_running = false; // 停止流水灯
        // 返回前关闭所有LED
        if (g_led_fd != -1) {
            ioctl(g_led_fd, GEC_LED1, LED_CMD_OFF);
            ioctl(g_led_fd, GEC_LED2, LED_CMD_OFF);
            ioctl(g_led_fd, GEC_LED3, LED_CMD_OFF);
            ioctl(g_led_fd, GEC_LED4, LED_CMD_OFF);
        }
        g_app_state = STATE_MAIN_MENU;
        return;
    }

    if (g_led_fd == -1) {
        printf("LED device not available.\n");
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_led_on)) {
        printf("LED ON clicked.\n");
        g_is_marquee_running = false;
        ioctl(g_led_fd, GEC_LED1, LED_CMD_ON);
        ioctl(g_led_fd, GEC_LED2, LED_CMD_ON);
        ioctl(g_led_fd, GEC_LED3, LED_CMD_ON);
        ioctl(g_led_fd, GEC_LED4, LED_CMD_ON);
    } else if (is_touch_in_rect(x, y, &g_rect_led_off)) {
        printf("LED OFF clicked.\n");
        g_is_marquee_running = false;
        ioctl(g_led_fd, GEC_LED1, LED_CMD_OFF);
        ioctl(g_led_fd, GEC_LED2, LED_CMD_OFF);
        ioctl(g_led_fd, GEC_LED3, LED_CMD_OFF);
        ioctl(g_led_fd, GEC_LED4, LED_CMD_OFF);
    } else if (is_touch_in_rect(x, y, &g_rect_led_marquee)) {
        printf("LED Marquee clicked.\n");
        g_is_marquee_running = true;
        g_marquee_led_index = 0; // 从第一个灯开始
    }
}

// 处理音频播放器界面的触摸事件
void handle_touch_audio_player(int x, int y) {
    // 检查是否点击了语音识别按钮（右上角）
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger_subviews)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
        return;
    }
    
    if (is_touch_in_rect(x, y, &g_rect_back_from_audio)) {
        stop_video(); // 返回时，只停止视频
        g_app_state = STATE_MAIN_MENU;
        return;
    }

    if (g_audio_count <= 0) {
        printf("No audio files found in %s\n", AUDIO_DIR_PATH);
        return;
    }

    if (is_touch_in_rect(x, y, &g_rect_audio_play) || is_touch_in_rect(x, y, &g_rect_audio_resume)) {
        if (g_current_audio_index == -1) { // 从未播放过
            start_audio(0); // 改为调用start_audio
        } else if (g_is_paused) { // 正处于暂停状态，则恢复
            send_media_cmd("pause\n");
            if (g_video_mplayer_pid > 0) { // 同步恢复视频
                kill(g_video_mplayer_pid, SIGCONT);
            }
            g_is_paused = false;
            gettimeofday(&g_playback_start_time, NULL); // 重置计时起点
        }
    } else if (is_touch_in_rect(x, y, &g_rect_audio_pause)) {
        if (!g_is_paused && g_audio_mplayer_pid > 0) { // 正在播放，则暂停
            send_media_cmd("pause\n");
            if (g_video_mplayer_pid > 0) { // 同步暂停视频
                kill(g_video_mplayer_pid, SIGSTOP);
            }
            g_is_paused = true;
            // 计算从上次开始/恢复播放到此刻的时间，并累加
            struct timeval now;
            gettimeofday(&now, NULL);
            float elapsed = (now.tv_sec - g_playback_start_time.tv_sec) + (now.tv_usec - g_playback_start_time.tv_usec) / 1000000.0f;
            g_current_seconds += elapsed;
        }
    } else if (is_touch_in_rect(x, y, &g_rect_audio_mute)) {
        send_media_cmd("mute\n");
    } else if (is_touch_in_rect(x, y, &g_rect_audio_vol_up)) {
        send_media_cmd("volume +5\n");
    } else if (is_touch_in_rect(x, y, &g_rect_audio_vol_down)) {
        send_media_cmd("volume -5\n");
    } else if (is_touch_in_rect(x, y, &g_rect_audio_seek_f)) {
        // 手动计时模式下，快进/快退变得复杂，暂时简化为更新计时器
        if (g_current_seconds + 10.0f < g_total_seconds) {
            g_current_seconds += 10.0f;
            send_media_cmd("seek +10 0\n");
        }
    } else if (is_touch_in_rect(x, y, &g_rect_audio_seek_b)) {
        g_current_seconds -= 10.0f;
        if (g_current_seconds < 0) g_current_seconds = 0;
        send_media_cmd("seek -10 0\n");
    } else if (is_touch_in_rect(x, y, &g_rect_audio_next)) {
        start_audio((g_current_audio_index + 1) % g_audio_count);
    } else if (is_touch_in_rect(x, y, &g_rect_audio_prev)) {
        start_audio((g_current_audio_index - 1 + g_audio_count) % g_audio_count);
    }
}

//  ：处理空调界面的触摸事件
void handle_touch_ac_view(int x, int y) {
    // 检查是否点击了语音识别按钮（右上角）
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger_subviews)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
        return;
    }
    
    if (is_touch_in_rect(x, y, &g_rect_back_from_ac)) {
        g_app_state = STATE_MAIN_MENU;
        return;
    }

    if (g_beep_fd == -1) {
        printf("Beeper device not available.\n");
        return; // 如果蜂鸣器不可用，直接返回
    }

    // 为按键提示音定义一个固定的音量（50%），避免受蜂鸣器界面音量设置的影响
    const int click_sound_duty_cycle = 500000;

    if (is_touch_in_rect(x, y, &g_rect_ac_on)) {
        printf("A/C ON button clicked.\n");
        // 使用独立的、固定的音量来播放提示音
        ioctl(g_beep_fd, BEEP_ON, 1);
        set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, click_sound_duty_cycle);
        usleep(300000); // 响 0.3 秒
        set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
        ioctl(g_beep_fd, BEEP_OFF, 1);

    } else if (is_touch_in_rect(x, y, &g_rect_ac_off)) {
        printf("A/C OFF button clicked.\n");
        // 响第一声
        ioctl(g_beep_fd, BEEP_ON, 1);
        set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, click_sound_duty_cycle);
        usleep(300000);
        set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
        ioctl(g_beep_fd, BEEP_OFF, 1);
        
        usleep(150000); // 间隔 0.15 秒

        // 响第二声
        ioctl(g_beep_fd, BEEP_ON, 1);
        set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, click_sound_duty_cycle);
        usleep(300000);
        set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
        ioctl(g_beep_fd, BEEP_OFF, 1);
    }
}
//  ：新增：处理摄像头预览界面的触摸事件
void handle_touch_camera_view(int x, int y) {
    // 检查是否点击了右上角的语音识别按钮
    if (is_touch_in_rect(x, y, &g_rect_voice_client_trigger_camera)) {
        printf("语音识别按钮点击! 正在后台启动客户端...\n");
        
        pid_t pid = fork();
        if (pid == -1) {
            perror("fork for voice client failed");
        } else if (pid == 0) { // 子进程
            // 假设客户端可执行文件位于项目根目录下的 bin/ 文件夹中
            execlp("./bin/client", "client", NULL);
            // 如果 execlp 执行成功, 此处代码不会被执行
            perror("execlp ./bin/client failed");
            exit(1); 
        } else { // 父进程
            // 使用 g_external_app_pid 来跟踪这个后台进程
            g_external_app_pid = pid;
            printf("语音识别客户端已作为后台进程启动 (PID: %d)\n", g_external_app_pid);
        }
        return;
    }
    
    // 检查是否点击了左上角的返回按钮
    if (is_touch_in_rect(x, y, &g_rect_back_from_camera)) {
        printf("Back from camera view clicked.\n");
        // 停止 mplayer 进程
        if (g_camera_view_pid > 0) {
            kill(g_camera_view_pid, SIGKILL);
            waitpid(g_camera_view_pid, NULL, 0);
            g_camera_view_pid = -1;
        }
        // 切换回主菜单
        g_app_state = STATE_MAIN_MENU;
    }
}


// 处理绘图界面的触摸事件
void handle_touch_paint_view(struct input_event *ev) {
    static int final_x = 0, final_y = 0;
    static bool pending_draw = false; //  ：标记是否有待处理的绘图操作
//对坐标事件
    if (ev->type == EV_ABS) {
        if (ev->code == ABS_X) final_x = ev->value;// 缓存X坐标
        if (ev->code == ABS_Y) final_y = ev->value;
        // 收到坐标更新后，只做标记，不立即绘图
        if (g_is_drawing) {
            pending_draw = true;
        }
        return; // EV_ABS事件处理到此为止
    }

    // 仅当收到同步事件时，才执行绘图操作
    if (g_is_drawing && ev->type == EV_SYN) {
        if (!pending_draw) return; // 如果没有坐标更新，则无需绘图

        int tx = final_x, ty = final_y;
        transform_touch_coords(&tx, &ty);

        if(tx == g_last_x && ty == g_last_y) return;

        switch (g_current_tool) {
            case TOOL_PENCIL:  //两次复制
                 draw_line_ex(g_paint_canvas_buffer, g_last_x, g_last_y, tx, ty, 3, g_draw_color);//3像素的线
                 memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);//将画布缓冲区（g_paint_canvas_buffer）的内容复制到屏幕缓冲区（g_screen_buffer）。
                 display_on_lcd();
                 break;
            case TOOL_ERASER:
                 draw_line_ex(g_paint_canvas_buffer, g_last_x, g_last_y, tx, ty, 20, 0x00FFFFFF);//白色
                 memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                 display_on_lcd();
                 break;
            case TOOL_LINE:
            case TOOL_RECT:
            case TOOL_CIRCLE:
                // 拖动时的预览绘制
                memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                if (g_current_tool == TOOL_LINE) {
                    draw_line_ex(g_screen_buffer, g_start_x, g_start_y, tx, ty, 3, g_draw_color);
                } else if (g_current_tool == TOOL_CIRCLE) {
                    int r = (int)sqrt(pow(tx - g_start_x, 2) + pow(ty - g_start_y, 2));
                    draw_thick_circle(g_screen_buffer, g_start_x, g_start_y, r, g_draw_color);
                } else { // TOOL_RECT
                    draw_line_ex(g_screen_buffer, g_start_x, g_start_y, tx, g_start_y, 3, g_draw_color);
                    draw_line_ex(g_screen_buffer, g_start_x, g_start_y, g_start_x, ty, 3, g_draw_color);
                    draw_line_ex(g_screen_buffer, tx, g_start_y, tx, ty, 3, g_draw_color);
                    draw_line_ex(g_screen_buffer, g_start_x, ty, tx, ty, 3, g_draw_color);
                }
                display_on_lcd();
                break;
        }
        g_last_x = tx;
        g_last_y = ty;
        pending_draw = false; // 重置标记
        return;
    }

    // 处理按键（按下/抬起）事件
    if (ev->type == EV_KEY && ev->code == BTN_TOUCH) {
        int tx = final_x, ty = final_y;
        transform_touch_coords(&tx, &ty);

        // 如果正在确认退出
        if (g_is_confirming_exit) {
            if (ev->value == 0) { // 只在抬起时响应
                if (is_touch_in_rect(tx, ty, &g_rect_confirm_yes)) {
                    printf("Dialog: YES clicked. Saving and returning.\n");
                    if (save_canvas_to_bmp("/root/bmpdk") == 0) {
                         printf("Canvas saved successfully!\n");
                    } else {
                         printf("Failed to save canvas.\n");
                    }
                    g_is_confirming_exit = false;
                    g_app_state = STATE_MAIN_MENU;
                } else if (is_touch_in_rect(tx, ty, &g_rect_confirm_no)) {
                    printf("Dialog: NO clicked. Returning without saving.\n");
                    g_is_confirming_exit = false;
                    g_app_state = STATE_MAIN_MENU;
                } else {
                    // 点击对话框外部，则取消操作，恢复画布
                    printf("Dialog: Clicked outside. Cancelling.\n");
                    g_is_confirming_exit = false;
                    memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                    display_on_lcd();
                }
            }
            return; // 消费掉此事件
        }

        if (ev->value == 1) { // 按下
            // 如果正在选择颜色
            if (g_is_selecting_color) {
                // 检查是否点击了某个色块
                for (int i = 0; i < NUM_COLORS; i++) {
                    if (is_touch_in_rect(tx, ty, &g_rect_color_blocks[i])) {
                        g_current_color_index = i;
                        g_draw_color = g_color_palette[i];
                        printf("Color selected: index %d\n", i);
                        
                        // 隐藏调色板并恢复画布
                        g_is_selecting_color = false;
                        memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                        display_on_lcd();
                        return; // 事件处理完毕
                    }
                }
                // 如果点击的不是色块，则隐藏调色板
                g_is_selecting_color = false;
                memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                display_on_lcd();
                // 不返回，让事件继续，以便可以在隐藏调色板后立即开始绘图
            }

            // 检查是否点击工具栏
            if (is_touch_in_rect(tx, ty, &g_rect_back_from_paint)) { /* 退出按钮，什么都不做，等抬起时处理 */ }
            else if (is_touch_in_rect(tx, ty, &g_rect_tool_pencil)) {
                g_current_tool = TOOL_PENCIL;
                printf("Tool: Pencil selected.\n");
            }
            else if (is_touch_in_rect(tx, ty, &g_rect_tool_circle)) { g_current_tool = TOOL_CIRCLE; printf("Tool: Circle\n"); }
            else if (is_touch_in_rect(tx, ty, &g_rect_tool_rect))   { g_current_tool = TOOL_RECT;   printf("Tool: Rectangle\n"); }
            else if (is_touch_in_rect(tx, ty, &g_rect_tool_line))   { g_current_tool = TOOL_LINE;   printf("Tool: Line\n"); }
            else if (is_touch_in_rect(tx, ty, &g_rect_tool_eraser)) { g_current_tool = TOOL_ERASER; printf("Tool: Eraser\n"); }
            else { // 在画布上按下
                g_is_drawing = true;
                g_start_x = tx; g_start_y = ty;
                g_last_x = tx; g_last_y = ty;
                 if(g_current_tool == TOOL_PENCIL) { //画笔按下就画一个点
                    draw_line_ex(g_paint_canvas_buffer, tx, ty, tx, ty, 3, g_draw_color);
                    memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                    display_on_lcd();
                }
            }
        } else if (ev->value == 0) { // 抬起
            if (is_touch_in_rect(tx, ty, &g_rect_back_from_paint)) {
                 printf("Back button clicked, showing confirmation dialog.\n");
                 g_is_confirming_exit = true;
                 render_confirm_dialog();
                 display_on_lcd();
            } else if (g_is_drawing) {
                 switch (g_current_tool) {
                    case TOOL_LINE:
                        draw_line_ex(g_paint_canvas_buffer, g_start_x, g_start_y, tx, ty, 3, g_draw_color);
                        break;
                    case TOOL_RECT:
                        draw_line_ex(g_paint_canvas_buffer, g_start_x, g_start_y, tx, g_start_y, 3, g_draw_color);
                        draw_line_ex(g_paint_canvas_buffer, g_start_x, g_start_y, g_start_x, ty, 3, g_draw_color);
                        draw_line_ex(g_paint_canvas_buffer, tx, g_start_y, tx, ty, 3, g_draw_color);
                        draw_line_ex(g_paint_canvas_buffer, g_start_x, ty, tx, ty, 3, g_draw_color);
                        break;
                    case TOOL_CIRCLE:
                        {
                            int r = (int)sqrt(pow(tx - g_start_x, 2) + pow(ty - g_start_y, 2));
                            draw_thick_circle(g_paint_canvas_buffer, g_start_x, g_start_y, r, g_draw_color);
                        }
                        break;
                    default: break;
                }
                memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                display_on_lcd();
            }
            g_is_drawing = false;
        }
    }
}

void handle_touch_paint_view_double_click_logic(struct input_event *ev) {
     static struct timeval last_pencil_click_time = {0, 0};
     static int final_x = 0, final_y = 0;

     if (ev->type == EV_ABS) {
        if(ev->code == ABS_X) final_x = ev->value;
        if(ev->code == ABS_Y) final_y = ev->value;
     }

     if (ev->type == EV_KEY && ev->code == BTN_TOUCH && ev->value == 1) { // 只关心按下
        int tx = final_x, ty = final_y;
        transform_touch_coords(&tx, &ty);

        if (is_touch_in_rect(tx, ty, &g_rect_tool_pencil)) {
            struct timeval current_click_time;
            gettimeofday(&current_click_time, NULL);
            long time_diff_us = (current_click_time.tv_sec - last_pencil_click_time.tv_sec) * 1000000L +
                                (current_click_time.tv_usec - last_pencil_click_time.tv_usec);

            if (time_diff_us < 500000) { // 500ms 内第二次点击
                g_is_selecting_color = !g_is_selecting_color; // 切换调色板显示状态
                if(g_is_selecting_color) {
                    printf("Double click on pencil: showing color palette.\n");
                    render_color_palette();
                } else {
                    printf("Double click on pencil: hiding color palette.\n");
                    memcpy(g_screen_buffer, g_paint_canvas_buffer, LCD_SIZE);
                }
                display_on_lcd();
                
                // 重置时间，防止三击等
                last_pencil_click_time.tv_sec = 0;
                last_pencil_click_time.tv_usec = 0;
            } else {
                 last_pencil_click_time = current_click_time;
            }
        }
     }
}

// =================================================================================
// 9. 绘图算法
// =================================================================================

// 辅助函数：绘制一个指定大小的笔刷点（方形）
void draw_brush_point(int* buffer, int cx, int cy, int size, int color) {
    int half_size = size / 2;
    for (int y = cy - half_size; y <= cy + half_size; y++) {
        for (int x = cx - half_size; x <= cx + half_size; x++) {
            if (x >= 0 && x < LCD_WIDTH && y >= 0 && y < LCD_HEIGHT) {
                buffer[y * LCD_WIDTH + x] = color;
            }
        }
    }
}

// 使用Bresenham算法在缓冲区中画一条线 (支持笔刷大小)
void draw_line_ex(int* buffer, int x0, int y0, int x1, int y1, int size, int color) {
    int dx = abs(x1 - x0), sx = x0 < x1 ? 1 : -1;// x方向距离和步进方向
    int dy = -abs(y1 - y0), sy = y0 < y1 ? 1 : -1;// y方向距离（负）和步进方向
    int err = dx + dy, e2; // 误差项

    for (;;) {// 无限循环，直到终点
        draw_brush_point(buffer, x0, y0, size, color); // 在当前点绘制指定宽度的像素块
        if (x0 == x1 && y0 == y1) break;//到达终点，退出循环
        e2 = 2 * err;// 误差项翻倍（放大偏差，方便判断）
        if (e2 >= dy) { err += dy; x0 += sx; }
        if (e2 <= dx) { err += dx; y0 += sy; }
    }
}

// 包装器：用于绘制1像素的细线（用于形状工具）
void draw_line(int* buffer, int x0, int y0, int x1, int y1, int color) {
    draw_line_ex(buffer, x0, y0, x1, y1, 1, color);
}

// 使用中点画圆算法在缓冲区画一个圆
void draw_circle(int* buffer, int xc, int yc, int r, int color) {
    if (r < 0) return;
    int x = r, y = 0;
    int err = 0;

    while (x >= y) {    // 只计算1/8圆（x从r减到0，y从0增到r，直到x < y）中点画圆
        // 绘制八个对称点
        if (xc + x < LCD_WIDTH && yc + y < LCD_HEIGHT) buffer[(yc + y) * LCD_WIDTH + (xc + x)] = color;
        if (xc - x >= 0 && yc + y < LCD_HEIGHT)        buffer[(yc + y) * LCD_WIDTH + (xc - x)] = color;
        if (xc + x < LCD_WIDTH && yc - y >= 0)         buffer[(yc - y) * LCD_WIDTH + (xc + x)] = color;
        if (xc - x >= 0 && yc - y >= 0)                buffer[(yc - y) * LCD_WIDTH + (xc - x)] = color;
        if (xc + y < LCD_WIDTH && yc + x < LCD_HEIGHT) buffer[(yc + x) * LCD_WIDTH + (xc + y)] = color;
        if (xc - y >= 0 && yc + x < LCD_HEIGHT)        buffer[(yc + x) * LCD_WIDTH + (xc - y)] = color;
        if (xc + y < LCD_WIDTH && yc - x >= 0)         buffer[(yc - x) * LCD_WIDTH + (xc + y)] = color;
        if (xc - y >= 0 && yc - x >= 0)                buffer[(yc - x) * LCD_WIDTH + (xc - y)] = color;

        if (err <= 0) {
            y += 1;
            err += 2 * y + 1;
        }
        if (err > 0) {
            x -= 1;
            err -= 2 * x + 1;
        }
    }
}

//  ：绘制一个3像素粗的圆
void draw_thick_circle(int* buffer, int xc, int yc, int r, int color) {
    draw_circle(buffer, xc, yc, r, color);
    if (r > 0) {
        draw_circle(buffer, xc, yc, r - 1, color);
    }
    draw_circle(buffer, xc, yc, r + 1, color);
}

// =================================================================================
// 10.  /重构: 音视频播放控制与进度条渲染
// =================================================================================

// 辅助函数: 确保FIFO管道已打开 
int ensure_fifo_open(void) {
    if (g_command_fifo_fd != -1) return g_command_fifo_fd;
    if (g_audio_mplayer_pid <= 0) return -1;

    int attempts = 0;
    while ((g_command_fifo_fd = open(COMMAND_FIFO_PATH, O_WRONLY | O_NONBLOCK)) == -1) {
        if (attempts >= 10) {
            perror("[FIFO] Error: Could not open FIFO after multiple attempts");
            g_command_fifo_fd = -1;
            return -1;
        }
        usleep(100000);
        attempts++;
    }
    return g_command_fifo_fd;
}

// 发送命令到mplayer进程 
void send_media_cmd(const char* cmd) {
    if (ensure_fifo_open() != -1) {
        if (write(g_command_fifo_fd, cmd, strlen(cmd)) == -1) {
            if (errno != EAGAIN) {
                perror("write to fifo failed, closing pipe");
                close(g_command_fifo_fd);
                g_command_fifo_fd = -1;
            }
        }
    }
}

// 停止所有媒体播放 
void stop_media(void) {
    if (g_audio_mplayer_pid > 0) {
        kill(g_audio_mplayer_pid, SIGKILL);
        waitpid(g_audio_mplayer_pid, NULL, 0);
        g_audio_mplayer_pid = -1;
    }
    if (g_video_mplayer_pid > 0) {
        kill(g_video_mplayer_pid, SIGKILL);
        waitpid(g_video_mplayer_pid, NULL, 0);
        g_video_mplayer_pid = -1;
    }
    if (g_command_fifo_fd != -1) { close(g_command_fifo_fd); g_command_fifo_fd = -1; }
    if (g_mplayer_stdout_pipe_fd != -1) { close(g_mplayer_stdout_pipe_fd); g_mplayer_stdout_pipe_fd = -1; }
    
    unlink(COMMAND_FIFO_PATH);

    g_current_audio_index = -1;
    g_progress_percent = 0;
    g_total_seconds = 0.0;
    g_current_seconds = 0.0;
    g_is_paused = true;
    printf("All media players stopped.\n");
}

// 播放音频和视频 (已重构为双进程)
void play_media(int index) {
    if (index < 0 || index >= g_audio_count) return;

    stop_media();

    // --- 1. 启动视频播放进程 (fire and forget) ---
    g_video_mplayer_pid = fork();
    if (g_video_mplayer_pid == 0) { // 子进程 - 视频
        int dev_null = open("/dev/null", O_WRONLY);
        if(dev_null != -1) {
            dup2(dev_null, STDOUT_FILENO); // 屏蔽所有输出
            dup2(dev_null, STDERR_FILENO);
        }
        execlp("mplayer", "mplayer",
               "-vo", "fbdev2:/dev/fb0", // 视频输出到framebuffer
               "-nosound",               // 关键：不处理音频
               "-loop", "0",             // 无限循环
               "-geometry", "0:0",       // 视频位置
               "-zoom", "-x", "800", "-y", "400", // 视频尺寸
               VIDEO_FILE_PATH,
               NULL);
        perror("execlp for video mplayer failed");
        exit(1);
    } else if (g_video_mplayer_pid < 0) {
        perror("fork for video mplayer failed");
        return; // 视频播放失败，则不继续
    }
    printf("Video player process started with PID %d\n", g_video_mplayer_pid);


    // --- 2. 启动音频播放进程 (可控) ---
    g_current_audio_index = index;
    g_total_seconds = (g_audio_files[index].duration > 1.0f) ? g_audio_files[index].duration : 300.0f;
    g_current_seconds = 0.0f;
    g_progress_percent = 0;
    g_is_paused = false;
    gettimeofday(&g_playback_start_time, NULL);

    mkfifo(COMMAND_FIFO_PATH, 0777);

    int p_fds[2];
    if (pipe(p_fds) == -1) { perror("pipe for audio progress failed"); return; }

    g_audio_mplayer_pid = fork();
    if (g_audio_mplayer_pid == 0) { // 子进程 - 音频
        close(p_fds[0]);
        dup2(p_fds[1], STDOUT_FILENO);
        int dev_null = open("/dev/null", O_WRONLY);
        if(dev_null != -1) dup2(dev_null, STDERR_FILENO);
        
        execlp("mplayer", "mplayer",
               "-slave", "-quiet",
               "-novideo",             // 关键：不处理视频
               "-input", "file=" COMMAND_FIFO_PATH,
               g_audio_files[g_current_audio_index].path,
               NULL);

        perror("execlp for audio mplayer failed");
        exit(1);

    } else if (g_audio_mplayer_pid < 0) {
        perror("fork for audio mplayer failed");
        close(p_fds[0]); close(p_fds[1]);
        stop_media(); // 清理已经启动的视频进程
        return;
    }

    // --- 父进程 ---
    close(p_fds[1]);
    g_mplayer_stdout_pipe_fd = p_fds[0];
    fcntl(g_mplayer_stdout_pipe_fd, F_SETFL, O_NONBLOCK);
    printf("Audio player process started with PID %d\n", g_audio_mplayer_pid);
}

// =================================================================================
// 11.  ：文件保存功能
// =================================================================================

/**
 * @brief 确保一个目录存在，如果不存在则创建它。
 * @param path 目录的完整路径。
 */
static void ensure_directory_exists(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == 0) { // 0755: rwxr-xr-x
            printf("目录 '%s' 已创建。\n", path);
        } else {
            char error_msg[128];
            snprintf(error_msg, sizeof(error_msg), "创建目录 '%s' 失败", path);
            perror(error_msg);
        }
    }
}

/**
 * @brief 处理拍照请求，将当前屏幕保存为带时间戳的BMP文件。
 */
static void handle_camera_snapshot(void) {
    const char* directory_path = "/root/bmpdk";
    ensure_directory_exists(directory_path);

    time_t now = time(NULL);
    struct tm *t = localtime(&now);
    if (t == NULL) {
        perror("localtime failed");
        return;
    }
    
    char filename[128];
    // 文件名格式：photo_YYYYMMDD_HHMMSS.bmp
    strftime(filename, sizeof(filename), "photo_%Y%m%d_%H%M%S.bmp", t);
    
    char full_path[256];
    sprintf(full_path, "%s/%s", directory_path, filename);

    printf("正在保存快照到: %s\n", full_path);

    if (capture_screen_to_bmp(full_path) == 0) {
        printf("快照保存成功！\n");
        //  ：新增：调用函数将照片发送到虚拟机
        printf("正在将快照发送到虚拟机...\n");
        send_image_to_vm(full_path);
    } else {
        fprintf(stderr, "错误: 保存快照失败。\n");
    }
}

int save_canvas_to_bmp(const char* directory_path) {
    // 1. 生成文件名
    time_t now = time(NULL);
    struct tm *t = localtime(&now);
    char filename[128];
    strftime(filename, sizeof(filename) - 1, "paint_%Y%m%d_%H%M%S.bmp", t);
    
    char full_path[256];
    sprintf(full_path, "%s/%s", directory_path, filename);

    // 2. 创建一个临时的纯白画布用于保存
    int* pure_canvas = malloc(LCD_SIZE);
    if (!pure_canvas) {
        perror("malloc for pure canvas failed");
        return -1;
    }
    for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) {
        pure_canvas[i] = 0x00FFFFFF; // 填充为白色
    }

    // 3. 加载原始背景图用于比较
    int* bg_buffer = malloc(LCD_SIZE);
    if (!bg_buffer) {
        perror("malloc for background buffer failed");
        free(pure_canvas);
        return -1;
    }
    if (draw_bmp_to_buffer(HUIHUA_BG_PATH, bg_buffer, 0, 0) != 0) {
        printf("Error: Could not load original background for comparison.\n");
        free(pure_canvas);
        free(bg_buffer);
        return -1;
    }

    // 4. 比较当前画布与原始背景，提取用户的笔迹
    // 只处理 y < 409 的区域，以忽略底部的工具栏
    for (int i = 0; i < LCD_WIDTH * 409; i++) {
        // 若当前画布的像素与原始背景不同，说明是用户绘制的，复制到临时画布
        if (g_paint_canvas_buffer[i] != bg_buffer[i]) {
            pure_canvas[i] = g_paint_canvas_buffer[i];
        }
    }
    free(bg_buffer); // 比较完毕，释放背景缓冲区

    // 5. 打开文件
    FILE* f = fopen(full_path, "wb");
    if (!f) {
        perror("fopen for saving bmp failed");
        free(pure_canvas);
        return -1;
    }

    // 6. 准备并写入BMP头
    //  :  : 修复: 添加 width 和 height 的定义
    int width = LCD_WIDTH;
    int height = 409; // 只保存画布区域，不包括底部工具栏

    // 计算BMP图像的行对齐字节数（BMP要求每行字节数是4的倍数）
    int row_padded = (width * 3 + 3) & ~3;  // 对齐公式：(宽*3 + 3) 再 & ~3（去掉二进制最后两位，确保是4的倍数）
    int image_size = row_padded * height;  // 图像数据总大小（对齐后的行字节数 * 高度）

    // BMP文件头（14字节）：描述文件整体信息
    BMPFileHeader fh;
    fh.signature[0] = 'B';  // BMP文件标识（必须是"BM"）
    fh.signature[1] = 'M';
    fh.fileSize = 54 + image_size;  // 文件总大小 = 头部（54字节） + 图像数据大小
    fh.reserved1 = 0;  // 保留字段（必须为0）
    fh.reserved2 = 0;
    fh.dataOffset = 54;  // 图像数据开始的偏移量（头部占54字节）

    // BMP信息头（40字节）：描述图像详细信息
    BMPInfoHeader fih;
    fih.size = sizeof(BMPInfoHeader);  // 信息头大小（40字节）
    fih.width = width;  // 图像宽度（LCD_WIDTH=800）
    fih.height = height;  // 图像高度（LCD_HEIGHT=480）
    fih.planes = 1;  // 色彩平面数（固定为1）
    fih.bitCount = 24;  // 颜色位数（24位真彩色，无alpha通道）
    fih.compression = 0;  // 压缩方式（0表示不压缩）
    fih.imageSize = image_size;  // 图像数据大小（同前面计算）
    fih.xPixelsPerMeter = 0;  // 水平分辨率（0表示忽略）
    fih.yPixelsPerMeter = 0;  // 垂直分辨率（0表示忽略）
    fih.colorsUsed = 0;  // 使用的颜色数（0表示全部）
    fih.colorsImportant = 0;  // 重要颜色数（0表示全部重要）

    // 写入文件头和信息头到文件
    fwrite(&fh, sizeof(fh), 1, f);
    fwrite(&fih, sizeof(fih), 1, f);

    // 7. 写入像素数据 (从纯白画布写入)
    // 分配一行像素的内存（按对齐后的字节数）
    unsigned char* row_data = (unsigned char*)malloc(row_padded);
    if (!row_data) {  // 内存分配失败处理
        fclose(f);
        free(pure_canvas);
        return -1;
    }
    // 按行写入像素（BMP像素存储顺序：从下到上、从左到右）
    for (int y = height - 1; y >= 0; y--) {  // y从最大高度开始（底部行），逐步向上
        for (int x = 0; x < width; x++) {  // 每行从左到右
            // 获取临时画布中(x,y)位置的像素颜色（ARGB格式）
            int pixel_color = pure_canvas[y * width + x];
            // BMP像素存储顺序是"蓝、绿、红"（与内存中的RGB顺序相反）
            row_data[x * 3] = pixel_color & 0xFF;  // 提取蓝色分量（最低8位）
            row_data[x * 3 + 1] = (pixel_color >> 8) & 0xFF;  // 提取绿色分量（中间8位）
            row_data[x * 3 + 2] = (pixel_color >> 16) & 0xFF;  // 提取红色分量（高8位）
        }
        // 写入一行像素数据（包含对齐的填充字节）
        fwrite(row_data, 1, row_padded, f);
    }

    // 8. 清理
    free(row_data);
    free(pure_canvas);
    fclose(f);

    return 0;
}

// =================================================================================
// 12. 初始化与主循环
// =================================================================================

// ---  : 移植自 key_control_led.c 的 sysfs 辅助函数 ---
static int write_to_sysfs_file(const char *path, const char *value) {
    int fd = open(path, O_WRONLY);
    if (fd == -1) {
        if (errno != EBUSY) { // EBUSY (Device or resource busy) 是常见且可忽略的错误
            char error_msg[128];
            snprintf(error_msg, sizeof(error_msg), "错误: 无法打开sysfs文件 '%s' 进行写入", path);
            perror(error_msg);
            return -1;
        }
    } else {
        if (write(fd, value, strlen(value)) == -1) {
            perror("错误: 无法写入sysfs文件");
            close(fd);
            return -1;
        }
        close(fd);
    }
    return 0;
}

static int setup_gpio_for_key(int gpio_num) {
    char buffer[64];
    
    // 1. 导出GPIO
    snprintf(buffer, sizeof(buffer), "%d", gpio_num);
    if (write_to_sysfs_file("/sys/class/gpio/export", buffer) != 0) {
        // 导出失败不一定是致命错误，可能已被导出
    }
    // 等待系统创建文件节点
    usleep(200000); 

    // 2. 设置为输入模式
    snprintf(buffer, sizeof(buffer), "/sys/class/gpio/gpio%d/direction", gpio_num);
    if (write_to_sysfs_file(buffer, "in") != 0) {
        fprintf(stderr, "错误: 无法设置 GPIO %d 的方向为 'in'\n", gpio_num);
        return -1;
    }

    return 0;
}

// ---  : 按键初始化函数 ---
static int initialize_keys(void) {
    // 检查是否禁用GPIO访问
    char *disable_gpio = getenv("DISABLE_GPIO");
    if (disable_gpio && strcmp(disable_gpio, "1") == 0) {
        printf("检测到DISABLE_GPIO=1环境变量，将禁用GPIO按键功能\n");
        // 初始化为无效值
        for (int i = 0; i < NUM_KEYS; i++) {
            g_key_fds[i] = -1;
            g_key_state[i] = KEY_STATE_IDLE;
            g_key_initial_state[i] = '1';
        }
        return 0;
    }

    printf("正在初始化实体按键...\n");
    for (int i = 0; i < NUM_KEYS; i++) {
        if (setup_gpio_for_key(g_key_gpios[i]) != 0) {
            return -1;
        }

        char path[64];
        snprintf(path, sizeof(path), "/sys/class/gpio/gpio%d/value", g_key_gpios[i]);
        g_key_fds[i] = open(path, O_RDONLY);
        if (g_key_fds[i] == -1) {
            fprintf(stderr, "错误: 无法打开 GPIO %d 的 value 文件\n", g_key_gpios[i]);
            return -1;
        }

        //  : 读取并存储初始电平
        if (read(g_key_fds[i], &g_key_initial_state[i], 1) != 1) {
             fprintf(stderr, "错误: 无法读取 GPIO %d 的初始电平\n", g_key_gpios[i]);
             g_key_initial_state[i] = '1'; // 假定一个安全的默认值
        }
        printf("  - GPIO %d 初始化完成, 初始电平: '%c'\n", g_key_gpios[i], g_key_initial_state[i]);

        // 初始化状态机
        g_key_state[i] = KEY_STATE_IDLE;
    }
    printf("实体按键初始化完成。\n");
    return 0;
}

// 初始化所有设备和资源
int initialize_app(void) {
    // --- 打开设备文件 ---
    g_lcd_fd = open(LCD_DEVICE, O_RDWR);
    if (g_lcd_fd == -1) { perror("open lcd failed"); return -1; }

    g_touch_fd = open(TOUCH_DEVICE, O_RDONLY);
    if (g_touch_fd == -1) { perror("open touch failed"); close(g_lcd_fd); return -1; }

    g_beep_fd = open(BEEP_DEVICE, O_RDWR);
    if (g_beep_fd == -1) { 
        perror("open beep failed, continuing without beeper"); 
    }
    
    g_led_fd = open(LED_DEVICE, O_RDWR);
    if (g_led_fd == -1) {
        perror("open led failed, continuing without led control");
    }
    
    // ---  : 打开RTC设备 ---
    g_rtc_fd = open("/dev/rtc0", O_RDONLY);
    if (g_rtc_fd == -1) {
        perror("警告: 无法打开RTC设备, 时间显示功能将不可用");
    } else {
        printf("成功打开RTC设备 /dev/rtc0。\n");
    }

    // ---  : 打开红外传感器设备 ---
    g_infrared_fd = open(INFRARED_DEVICE, O_RDONLY | O_NONBLOCK);
    if (g_infrared_fd == -1) {
        perror("警告: 无法打开红外设备, 接近检测功能将不可用");
    } else {
        printf("成功打开红外设备，接近检测功能已启用。\n");
    }
    
    // --- 分配内存 ---
    g_screen_buffer = (int*)malloc(LCD_SIZE);
    if (!g_screen_buffer) { perror("malloc screen buffer failed"); return -1; }
    g_paint_canvas_buffer = (int*)malloc(LCD_SIZE);
    if (!g_paint_canvas_buffer) { perror("malloc paint buffer failed"); return -1; }
    g_audio_bg_buffer = (int*)malloc(LCD_SIZE);
    if (!g_audio_bg_buffer) { perror("malloc audio bg buffer failed"); return -1; }
    g_main_menu_bg_buffer = (int*)malloc(LCD_SIZE);
    if (!g_main_menu_bg_buffer) { perror("malloc main menu bg buffer failed"); return -1; }

    // --- 定义主界面按钮点击区域 ---
    // Col 1
    g_rect_album = (Rect){START_X, START_Y, BTN_WIDTH, BTN_HEIGHT};
    g_rect_video = (Rect){START_X, START_Y + (BTN_HEIGHT + V_SPACING), BTN_WIDTH, BTN_HEIGHT};
    g_rect_led = (Rect){START_X, START_Y + 2 * (BTN_HEIGHT + V_SPACING), BTN_WIDTH, BTN_HEIGHT};
    g_rect_beep = (Rect){START_X, START_Y + 3 * (BTN_HEIGHT + V_SPACING), BTN_WIDTH, BTN_HEIGHT};
    // Col 2
    int col2_x = START_X + BTN_WIDTH + H_SPACING;
    g_rect_audio = (Rect){col2_x, START_Y, BTN_WIDTH, BTN_HEIGHT};
    g_rect_sensor = (Rect){col2_x, START_Y + (BTN_HEIGHT + V_SPACING), BTN_WIDTH, BTN_HEIGHT};
    g_rect_paint = (Rect){col2_x, START_Y + 2 * (BTN_HEIGHT + V_SPACING), BTN_WIDTH, BTN_HEIGHT};
    g_rect_ac = (Rect){col2_x, START_Y + 3 * (BTN_HEIGHT + V_SPACING), BTN_WIDTH, BTN_HEIGHT};

    // ---  : 定义主界面亮度控制按钮区域 ---
    g_rect_brightness_down = (Rect){0, LCD_HEIGHT - 60, 60, 60};
    g_rect_brightness_up = (Rect){LCD_WIDTH - 60, LCD_HEIGHT - 60, 60, 60};

    // ---  ：新增：定义主界面摄像头按钮区域 ---
    g_rect_camera_entry = (Rect){LCD_WIDTH - 60, 0, 60, 60};

    //  ：新增：定义语音客户端触发按钮区域 (左上角)
    g_rect_voice_client_trigger = (Rect){0, 0, 60, 60};

    // --- 定义传感器界面返回按钮区域 ---
    g_rect_back_from_sensor = (Rect){0, 0, 55, 59};

    // --- 定义蜂鸣器界面按钮区域 ---
    g_rect_back_from_beeper = (Rect){0, 0, 64, 68};
    g_rect_beep_on = (Rect){125, 91, 100, 105};
    g_rect_beep_off = (Rect){125, 246, 100, 105};
    g_rect_beep_vol_down = (Rect){549, 246, 100, 105};
    g_rect_beep_vol_up = (Rect){549, 91, 100, 105};

    // --- 定义LED界面按钮区域 ---
    g_rect_led_on = (Rect){464, 160, 80, 85};
    g_rect_led_off = (Rect){567, 160, 80, 85};
    g_rect_led_marquee = (Rect){670, 160, 80, 85};
    g_rect_back_from_led = (Rect){0, 0, 64, 68};

    // --- 定义音频播放器按钮区域 ---
    int audio_btn_y = 480 - 68;
    int audio_btn_x = 80;
    g_rect_back_from_audio = (Rect){0, 0, 64, 68};
    g_rect_audio_play   = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_pause  = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_resume = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_mute   = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_vol_up = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_vol_down = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_seek_f = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_seek_b = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_prev   = (Rect){audio_btn_x, audio_btn_y, 64, 68}; audio_btn_x += 64;
    g_rect_audio_next   = (Rect){audio_btn_x, audio_btn_y, 64, 68};

    // --- 定义绘图界面工具栏按钮区域 ---
    g_rect_tool_pencil = (Rect){0, 480-71, 64, 71};
    g_rect_tool_circle = (Rect){64, 480-71, 64, 71};
    g_rect_tool_rect = (Rect){128, 480-71, 64, 71};
    g_rect_tool_line = (Rect){192, 480-71, 64, 71};
    g_rect_tool_eraser = (Rect){256, 480-71, 64, 71};
    g_rect_back_from_paint = (Rect){800-55, 480-59, 55, 59};

    // ---  ：定义调色板色块区域 ---
    int color_block_y_row1 = 480 - 71 - 40 - 10; // 下面一行
    int color_block_y_row2 = color_block_y_row1 - 40 - 10; // 上面一行

    for(int i=0; i<5; i++) { // 下一行
        g_rect_color_blocks[i] = (Rect){10 + i * (40 + 10), color_block_y_row1, 40, 40};
    }
    for(int i=0; i<5; i++) { // 上一行
        g_rect_color_blocks[i+5] = (Rect){10 + i * (40 + 10), color_block_y_row2, 40, 40};
    }

    // ---  ：定义确认对话框区域 ---
    g_rect_confirm_dialog = (Rect){250, 165, 300, 150};
    g_rect_confirm_yes = (Rect){g_rect_confirm_dialog.x + 40, g_rect_confirm_dialog.y + 80, 80, 40};
    g_rect_confirm_no = (Rect){g_rect_confirm_dialog.x + 180, g_rect_confirm_dialog.y + 80, 80, 40};

    // --- 扫描音频文件 ---
    DIR *dir = opendir(AUDIO_DIR_PATH);
    if(dir) {
        struct dirent *entry;
        while((entry = readdir(dir)) != NULL) {
            if (strcasestr(entry->d_name, ".mp3")) {
                g_audio_count++;
                g_audio_files = realloc(g_audio_files, g_audio_count * sizeof(AudioFile));
                char* path = malloc(strlen(AUDIO_DIR_PATH) + strlen(entry->d_name) + 2);
                sprintf(path, "%s/%s", AUDIO_DIR_PATH, entry->d_name);
                g_audio_files[g_audio_count-1].path = path;
                //  ：获取并存储音频时长
                g_audio_files[g_audio_count-1].duration = get_audio_duration(path);
                printf("Found audio: %s\n", path);
            }
        }
        closedir(dir);
    } else {
        perror("opendir for audio files failed");
    }
    printf("Found %d audio files in total.\n", g_audio_count);

    // 在加载其他背景图之前，先加载一次音乐播放器背景到它的专属缓冲区
    if (draw_bmp_to_buffer(AUDIO_BG_PATH, g_audio_bg_buffer, 0, 0) != 0) {
        // 如果失败，则填充黑色
        for(int i=0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_audio_bg_buffer[i] = 0;
    }

    // ---  : 定义空调界面按钮区域 ---
    g_rect_back_from_ac = (Rect){0, 0, 64, 68};
    g_rect_ac_on = (Rect){500, 59, 160, 171};
    g_rect_ac_off = (Rect){500, 263, 160, 171};

    // ---  : 初始化实体按键 ---
    if (initialize_keys() != 0) {
        fprintf(stderr, "致命错误: 实体按键初始化失败。\n");
        // 即使按键失败，也允许程序继续运行
    }

    //  : 加载主菜单背景到其专属缓冲区
    if (draw_bmp_to_buffer(BACKGROUND_BMP_PATH, g_main_menu_bg_buffer, 0, 0) != 0) {
        // 如果失败, 则填充深蓝色
        for (int i = 0; i < LCD_WIDTH * LCD_HEIGHT; i++) g_main_menu_bg_buffer[i] = 0x00112233;
    }

    // ---  : 手动使能LCD背光 (GPIOB24, 1*32+24=56) ---
    printf("正在手动使能LCD背光 (GPIO 56)...\n");
    
    // 检查是否禁用GPIO访问
    char *disable_gpio = getenv("DISABLE_GPIO");
    if (disable_gpio && strcmp(disable_gpio, "1") == 0) {
        printf("检测到DISABLE_GPIO=1环境变量，跳过LCD背光GPIO初始化\n");
    } else {
        if (setup_gpio_for_key(56) == 0) {
            char path[64];
            char value_path[64];
            
            snprintf(path, sizeof(path), "/sys/class/gpio/gpio56/direction");
            write_to_sysfs_file(path, "out");
            
            snprintf(value_path, sizeof(value_path), "/sys/class/gpio/gpio56/value");
            write_to_sysfs_file(value_path, "1");
            
            printf("LCD背光已手动使能。\n");
        } else {
            fprintf(stderr, "警告: 手动使能背光GPIO 56失败。\n");
        }
    }

    // ---  : 初始化背光 (最终方案: 完全手动) ---
    //  ：最终修改：不再检测标准接口，强制使用底层PWM0
    printf("背光初始化: 强制使用底层PWM0接口。\n");
    printf("设置初始亮度等级 %d/%zu\n", g_current_brightness_level + 1, (size_t)NUM_BRIGHTNESS_LEVELS);
    set_backlight_level(g_current_brightness_level);

    // ---  ：新增：定义摄像头界面返回按钮区域 ---
    g_rect_back_from_camera = (Rect){0, 0, 60, 60};

    // ---  ：新增：打开拍照按键设备 ---
    g_photo_key_fd = open(PHOTO_KEY_DEVICE, O_RDONLY | O_NONBLOCK);
    if (g_photo_key_fd == -1) {
        perror("警告: 无法打开拍照按键设备，拍照功能将不可用");
    } else {
        printf("成功打开拍照按键设备 %s。\n", PHOTO_KEY_DEVICE);
    }
    
    //  ：新增：定义摄像头界面专用的语音按钮区域 (右上角)
    g_rect_voice_client_trigger_camera = (Rect){LCD_WIDTH - 60, 0, 60, 60};
    
    //  ：新增：定义其他子界面专用的语音识别按钮区域 (右上角)
    g_rect_voice_client_trigger_subviews = (Rect){LCD_WIDTH - 60, 0, 60, 60};
    
    return 0;
}

// 清理所有资源
void cleanup_app() {
    stop_media(); // 确保退出时停止播放

    //  ：新增：如果摄像头预览正在运行，则关闭它
    if (g_camera_view_pid > 0) {
        printf("正在停止摄像头预览进程...\n");
        kill(g_camera_view_pid, SIGKILL);
        waitpid(g_camera_view_pid, NULL, 0);
        g_camera_view_pid = -1;
    }

    // ---  : 退出时关闭背光 ---
    printf("正在关闭屏幕背光...\n");
    set_backlight_level(-1); // 传入-1表示关闭

    // ---  : 手动关闭LCD背光使能 (设置为低电平) ---
    char value_path[64];
    snprintf(value_path, sizeof(value_path), "/sys/class/gpio/gpio56/value");
    
    // 检查是否禁用GPIO访问
    char *disable_gpio = getenv("DISABLE_GPIO");
    if (!(disable_gpio && strcmp(disable_gpio, "1") == 0)) {
        // 只有在未禁用GPIO访问时才尝试关闭
        write_to_sysfs_file(value_path, "0");
        write_to_sysfs_file("/sys/class/gpio/unexport", "56");
    }

    // ---  : 关闭实体按键 ---
    for (int i = 0; i < NUM_KEYS; i++) {
        if (g_key_fds[i] != -1) {
            close(g_key_fds[i]);
            g_key_fds[i] = -1;
        }
        
        // 检查是否禁用GPIO访问
        if (!(disable_gpio && strcmp(disable_gpio, "1") == 0)) {
            // 只有在未禁用GPIO访问时才尝试释放GPIO
            char buffer[16];
            snprintf(buffer, sizeof(buffer), "%d", g_key_gpios[i]);
            write_to_sysfs_file("/sys/class/gpio/unexport", buffer);
        }
    }

    if (g_audio_files) {
        for (int i = 0; i < g_audio_count; i++) free(g_audio_files[i].path);
        free(g_audio_files);
    }
    if (g_screen_buffer) free(g_screen_buffer);
    if (g_paint_canvas_buffer) free(g_paint_canvas_buffer);
    if (g_audio_bg_buffer) free(g_audio_bg_buffer); // 清理 的缓冲区
    if (g_main_menu_bg_buffer) free(g_main_menu_bg_buffer); //  : 清理主菜单背景缓冲区
    if (g_lcd_fd != -1) close(g_lcd_fd);
    if (g_touch_fd != -1) close(g_touch_fd);
    if (g_beep_fd != -1) close(g_beep_fd);
    if (g_led_fd != -1) close(g_led_fd);
    if (g_rtc_fd != -1) close(g_rtc_fd); //  : 关闭RTC设备
    if (g_infrared_fd != -1) close(g_infrared_fd); //  ：关闭红外设备
    if (g_photo_key_fd != -1) close(g_photo_key_fd); //  ：新增：关闭拍照按键设备
    
    printf("\n"); // 确保光标换行，避免弄乱终端提示符

    printf("Application cleaned up.\n");
}

//  ：设置PWM参数的辅助函数
void set_pwm_params(const char* device_path, int period, int duty_cycle) {
    int pwm_fd = open(device_path, O_WRONLY);//只写模式打开文件
    if (pwm_fd == -1) {
        perror("open pwm device for set_pwm_params failed"); 
        return;
    }
    char buffer[64];
    int len = snprintf(buffer, sizeof(buffer), "%d,%d\n", period, duty_cycle);
    if (write(pwm_fd, buffer, len) < 0) {
        perror("write to pwm device for set_pwm_params failed"); 
    }
    close(pwm_fd);
}

//  ：重构: 设置背光亮度等级 (最终修改：只保留底层PWM控制)
void set_backlight_level(int level) {
    int duty_cycle;
    if (level < 0) { 
        duty_cycle = 0;
        printf("正在关闭背光 PWM (底层方式)...\n");
    } else {
        if (level >= NUM_BRIGHTNESS_LEVELS) level = NUM_BRIGHTNESS_LEVELS - 1;
        if (level < 0) level = 0;
        g_current_brightness_level = level;
        duty_cycle = g_brightness_levels[level];
    }
    
    printf("设置背光 (底层方式): 等级 %d/%zu, 写入占空比: %d\n", 
           g_current_brightness_level + 1, (size_t)NUM_BRIGHTNESS_LEVELS, duty_cycle);
           
    set_pwm_params(BACKLIGHT_PWM_DEVICE, BACKLIGHT_PWM_PERIOD, duty_cycle);
}

// --- 重构后的独立控制函数 ---

// 停止音频播放
void stop_audio(void) {
    if (g_audio_mplayer_pid > 0) {
        kill(g_audio_mplayer_pid, SIGKILL);
        waitpid(g_audio_mplayer_pid, NULL, 0);
        g_audio_mplayer_pid = -1;
    }
    if (g_command_fifo_fd != -1) { close(g_command_fifo_fd); g_command_fifo_fd = -1; }
    if (g_mplayer_stdout_pipe_fd != -1) { close(g_mplayer_stdout_pipe_fd); g_mplayer_stdout_pipe_fd = -1; }
    unlink(COMMAND_FIFO_PATH);

    // 重置音频状态
    g_current_audio_index = -1;
    g_progress_percent = 0;
    g_total_seconds = 0.0;
    g_current_seconds = 0.0;
    g_is_paused = true;
    printf("Audio player stopped.\n");
}

// 停止视频播放
void stop_video(void) {
    if (g_video_mplayer_pid > 0) {
        kill(g_video_mplayer_pid, SIGKILL);
        waitpid(g_video_mplayer_pid, NULL, 0);
        g_video_mplayer_pid = -1;
        printf("Video player stopped.\n");
    }
}

// 启动背景视频
void start_video(void) {
    if (g_video_mplayer_pid > 0) {
        printf("Video player is already running.\n");
        return; // 已经在运行了
    }
    g_video_mplayer_pid = fork();
    if (g_video_mplayer_pid == 0) { // 子进程 - 视频
        int dev_null = open("/dev/null", O_WRONLY);
        if(dev_null != -1) {
            dup2(dev_null, STDOUT_FILENO);
            dup2(dev_null, STDERR_FILENO);
        }
        execlp("mplayer", "mplayer",
               "-vo", "fbdev2:/dev/fb0",
               "-nosound",
               "-loop", "0",
               "-geometry", "0:0",
               "-zoom", "-x", "800", "-y", "400",
               VIDEO_FILE_PATH,
               NULL);
        perror("execlp for video mplayer failed");
        exit(1);
    } else if (g_video_mplayer_pid < 0) {
        perror("fork for video mplayer failed");
    } else {
        printf("Video player process started with PID %d\n", g_video_mplayer_pid);
    }
}

// 播放一首指定的音频
void start_audio(int index) {
    if (index < 0 || index >= g_audio_count) return;

    // 先停止当前正在播放的音频（如果有）
    if (g_audio_mplayer_pid > 0) {
        kill(g_audio_mplayer_pid, SIGKILL);
        waitpid(g_audio_mplayer_pid, NULL, 0);
    }
    if (g_command_fifo_fd != -1) { close(g_command_fifo_fd); g_command_fifo_fd = -1; }
    if (g_mplayer_stdout_pipe_fd != -1) { close(g_mplayer_stdout_pipe_fd); g_mplayer_stdout_pipe_fd = -1; }
    unlink(COMMAND_FIFO_PATH);

    // --- 设置新音轨的状态 ---
        // --- 设置新音轨的状态 ---
        g_current_audio_index = index;  // 更新当前播放的音频索引
        g_total_seconds = (g_audio_files[index].duration > 1.0f) ? g_audio_files[index].duration : 300.0f;  // 总时长（默认300秒）
        g_current_seconds = 0.0f;  // 当前播放秒数（初始化为0）
        g_progress_percent = 0;  // 播放进度百分比（初始化为0）
        g_is_paused = false;  // 标记为"未暂停"状态
        gettimeofday(&g_playback_start_time, NULL);  // 记录播放开始时间（用于计算进度）

    // --- 启动新的音频播放进程 ---
    mkfifo(COMMAND_FIFO_PATH, 0777);//创建管道

    int p_fds[2];
    if (pipe(p_fds) == -1) { perror("pipe for audio progress failed"); return; }

        g_audio_mplayer_pid = fork();  // 创建子进程
        if (g_audio_mplayer_pid == 0) {  // 子进程：负责运行mplayer播放音频
            close(p_fds[0]);  // 关闭匿名管道的读端（子进程只写）
            dup2(p_fds[1], STDOUT_FILENO);  // 将子进程的stdout重定向到管道写端（父进程可读取）
            int dev_null = open("/dev/null", O_WRONLY);  // 打开"黑洞"设备，用于屏蔽输出
            if(dev_null != -1) dup2(dev_null, STDERR_FILENO);  // 将stderr重定向到/dev/null（屏蔽错误输出）
            
            // 启动mplayer，播放指定音频文件
            execlp("mplayer", "mplayer",
                "-slave",  // 从模式：接受外部命令（通过FIFO管道）
                "-quiet",  // 安静模式：减少冗余输出
                "-novideo",  // 不处理视频（只播放音频）
                "-input", "file=" COMMAND_FIFO_PATH,  // 指定命令输入管道（FIFO）
                g_audio_files[g_current_audio_index].path,  // 要播放的音频文件路径
                NULL);
            perror("execlp for audio mplayer failed");  // 若启动失败，报错
            exit(1);  // 子进程退出
        } else if (g_audio_mplayer_pid < 0) {  // fork失败
            perror("fork for audio mplayer failed");
            close(p_fds[0]); close(p_fds[1]);  // 关闭管道，清理资源
            return;
        }

    // --- 父进程 ---
    close(p_fds[1]);
    g_mplayer_stdout_pipe_fd = p_fds[0];
    fcntl(g_mplayer_stdout_pipe_fd, F_SETFL, O_NONBLOCK);
    printf("Audio player process started for '%s' with PID %d\n", g_audio_files[index].path, g_audio_mplayer_pid);
}

//  ：播放开机动画的函数 (采用fork后定时kill的最终方案)
void play_startup_animation(void) {
    // 估算一个比视频稍长的时间，单位：秒
    const int video_duration_estimate = 3;

    printf("正在播放开机动画 (预计 %d 秒)...\n", video_duration_estimate - 1);

    pid_t pid = fork();

    if (pid < 0) {
        perror("fork for startup animation failed");
        return;
    }

    if (pid == 0) { // 子进程
        // 子进程将自身替换为 mplayer
        execlp("mplayer", "mplayer",
               "-vo", "fbdev2:/dev/fb0",
               "-zoom", "-x", "800", "-y", "480",
               "-really-quiet",
               STARTUP_VIDEO_PATH,
               NULL);
        // 如果 execlp 执行成功，此行及以下代码不会被执行
        perror("execlp for mplayer failed");
        exit(1);
    } else { // 父进程
        // 不再使用 wait()，而是让主程序自己休眠
        sleep(video_duration_estimate);

        // 休眠结束后，主动、强制地结束mplayer子进程
        kill(pid, SIGKILL);
        waitpid(pid, NULL, 0); // 清理僵尸进程
        
        printf("开机动画播放完毕。\n");
    }
}

// =================================================================================
// 14.  : 重构后的主循环逻辑
// =================================================================================

// 专门处理 select() 和 I/O 事件
static void do_event_processing(void) {
    fd_set fds;
    FD_ZERO(&fds);
    FD_SET(g_touch_fd, &fds);
    int max_fd = g_touch_fd;

    if (g_infrared_fd != -1) {
        FD_SET(g_infrared_fd, &fds);
        if (g_infrared_fd > max_fd) max_fd = g_infrared_fd;
    }

    if (g_mplayer_stdout_pipe_fd != -1) {
        FD_SET(g_mplayer_stdout_pipe_fd, &fds);
        if (g_mplayer_stdout_pipe_fd > max_fd) max_fd = g_mplayer_stdout_pipe_fd;
    }

    //  ：新增：监听拍照按键
    if (g_photo_key_fd != -1) {
        FD_SET(g_photo_key_fd, &fds);
        if (g_photo_key_fd > max_fd) max_fd = g_photo_key_fd;
    }

    struct timeval tv = {0, 10000}; // 10ms timeout, a reasonable value
    int ret = select(max_fd + 1, &fds, NULL, NULL, &tv);

    if (ret < 0) {
        if (errno == EINTR) return;
        perror("select failed");
        g_app_state = STATE_EXIT;
        return;
    }

    if (ret == 0) return; // Timeout, no events

    // --- 触摸事件 ---
    if (FD_ISSET(g_touch_fd, &fds)) {
        struct input_event ev;
        static int touch_x = 0, touch_y = 0;
        if(g_app_state == STATE_PAINT_VIEW) {
             while(read(g_touch_fd, &ev, sizeof(ev)) > 0) {
                handle_touch_paint_view(&ev);
                handle_touch_paint_view_double_click_logic(&ev);
                if (g_app_state != STATE_PAINT_VIEW) break;
             }
        } else {
            if (read(g_touch_fd, &ev, sizeof(ev)) == sizeof(ev)) {
                if (ev.type == EV_ABS && ev.code == ABS_X) touch_x = ev.value;
                if (ev.type == EV_ABS && ev.code == ABS_Y) touch_y = ev.value;
                if (ev.type == EV_KEY && ev.code == BTN_TOUCH && ev.value == 0) {
                    int final_x = touch_x, final_y = touch_y;
                    transform_touch_coords(&final_x, &final_y);
                    switch(g_app_state) {
                        case STATE_MAIN_MENU:    handle_touch_main_menu(final_x, final_y);   break;
                        case STATE_SENSOR_VIEW:  handle_touch_sensor_view(final_x, final_y); break;
                        case STATE_BEEPER_VIEW:  handle_touch_beeper_view(final_x, final_y); break;
                        case STATE_LED_VIEW:     handle_touch_led_view(final_x, final_y);    break;
                        case STATE_AUDIO_PLAYER: handle_touch_audio_player(final_x, final_y);break;
                        case STATE_AC_VIEW:      handle_touch_ac_view(final_x, final_y);     break;
                        case STATE_CAMERA_VIEW:  handle_touch_camera_view(final_x, final_y); break; //  ：新增
                        case STATE_PAINT_VIEW:   handle_touch_paint_view(&ev);               break;
                        default: break;
                    }
                }
            }
        }
    }

    // --- 红外事件 ---
    if (g_infrared_fd != -1 && FD_ISSET(g_infrared_fd, &fds)) {
        char ir_buffer[32]; // 我们只需要探测到任何数据，而不需要关心其内容
        int bytes_read = read(g_infrared_fd, ir_buffer, sizeof(ir_buffer));

        if (bytes_read > 0) {
            // 检测到红外活动 (传感器可能被遮挡)
            struct timeval now;
            gettimeofday(&now, NULL);

            if (!g_is_person_present) {
                // 这是潜在"有人"状态的开始
                printf("Infrared: Activity detected, starting presence timer.\n");
                g_is_person_present = true; // 标记我们正在追踪一个"有人"事件
                g_infrared_continuous_start_time = now;
                g_infrared_alarm_triggered = false; // 为这个新的事件重置警报状态
            }
            
            // 始终更新最后一次检测到活动的时间
            g_last_infrared_activity_time = now;
        }
    }

    // --- MPlayer管道事件 ---
    if (g_mplayer_stdout_pipe_fd != -1 && FD_ISSET(g_mplayer_stdout_pipe_fd, &fds)) {
        char buf[128];
        if (read(g_mplayer_stdout_pipe_fd, buf, sizeof(buf)-1) == 0){
            printf("Audio MPlayer process terminated. Cleaning up audio resources.\n");
            if (g_audio_mplayer_pid > 0) waitpid(g_audio_mplayer_pid, NULL, 0);
            g_audio_mplayer_pid = -1;
            close(g_mplayer_stdout_pipe_fd);
            g_mplayer_stdout_pipe_fd = -1;
            close(g_command_fifo_fd);
            g_command_fifo_fd = -1;
            unlink(COMMAND_FIFO_PATH);
        }
    }

    // ---  ：新增：拍照按键事件 ---
    if (g_photo_key_fd != -1 && FD_ISSET(g_photo_key_fd, &fds)) {
        struct input_event ev;
        // 读取所有待处理的事件以避免延迟
        while (read(g_photo_key_fd, &ev, sizeof(ev)) > 0) {
            // 我们只关心按键释放事件来注册一次"点击"
            // 并且只在摄像头预览状态下响应
            if (g_app_state == STATE_CAMERA_VIEW && ev.type == EV_KEY && ev.value == 0) {
                printf("拍照按键被触发。\n");
                handle_camera_snapshot();
            }
        }
    }
}

// 专门处理周期性任务
static void do_periodic_tasks(void) {
    struct timeval now;
    gettimeofday(&now, NULL);//获取当前时间

    // ---  : 红外传感器周期性状态检查 ---
    if (g_infrared_fd != -1) {
        if (g_is_person_present) {
            // 当前我们正在追踪一个潜在的"有人"事件
            
            // 1. 检查持续占用时间是否达到触发警报的阈值
            if (!g_infrared_alarm_triggered) {
                double continuous_duration = (now.tv_sec - g_infrared_continuous_start_time.tv_sec) + 
                                             (now.tv_usec - g_infrared_continuous_start_time.tv_usec) / 1000000.0;
                
                if (continuous_duration >= INFRARED_DETECT_THRESHOLD_S) {
                    printf("Infrared: Person present for over %.1f seconds. Triggering alarm!\n", INFRARED_DETECT_THRESHOLD_S);
                    g_infrared_alarm_triggered = true;
                    
                    // 警报动作: 打开所有LED
                    if (g_led_fd != -1) {
                        ioctl(g_led_fd, GEC_LED1, LED_CMD_ON);
                        ioctl(g_led_fd, GEC_LED2, LED_CMD_ON);
                        ioctl(g_led_fd, GEC_LED3, LED_CMD_ON);
                        ioctl(g_led_fd, GEC_LED4, LED_CMD_ON);
                    }
                }
            }

            // 2. 检查一段时间内是否无活动，以判断人是否离开
            double silence_duration = (now.tv_sec - g_last_infrared_activity_time.tv_sec) + 
                                      (now.tv_usec - g_last_infrared_activity_time.tv_usec) / 1000000.0;

            if (silence_duration >= INFRARED_SILENCE_THRESHOLD_S) {
                printf("Infrared: No activity for over %.1f seconds. Person has left.\n", INFRARED_SILENCE_THRESHOLD_S);
                g_is_person_present = false;
                
                // 如果警报被触发了，就关闭它
                if (g_infrared_alarm_triggered) {
                    g_infrared_alarm_triggered = false; // 为下一次重置
                    
                    // 关闭警报动作
                    if (g_led_fd != -1) {
                        ioctl(g_led_fd, GEC_LED1, LED_CMD_OFF);
                        ioctl(g_led_fd, GEC_LED2, LED_CMD_OFF);
                        ioctl(g_led_fd, GEC_LED3, LED_CMD_OFF);
                        ioctl(g_led_fd, GEC_LED4, LED_CMD_OFF);
                    }
                }
            }
        }
    }

    // --- 其他周期性任务 ---
    switch(g_app_state) {
        case STATE_SENSOR_VIEW:
            if (now.tv_sec - g_last_sensor_refresh_time.tv_sec >= 2) {
                render_sensor_view();
                display_on_lcd();
                g_last_sensor_refresh_time = now;
            }
            break;
        case STATE_MAIN_MENU:
            if (now.tv_sec - g_last_time_update_time.tv_sec >= 1) {
                update_time_display();
                display_on_lcd();
                g_last_time_update_time = now;
            }
            break;
        case STATE_LED_VIEW:
            if (g_is_marquee_running && now.tv_sec - g_last_marquee_update_time.tv_sec >= 1) {
                if (g_led_fd != -1) {
                    ioctl(g_led_fd, GEC_LED1, LED_CMD_OFF);
                    ioctl(g_led_fd, GEC_LED2, LED_CMD_OFF);
                    ioctl(g_led_fd, GEC_LED3, LED_CMD_OFF);
                    ioctl(g_led_fd, GEC_LED4, LED_CMD_OFF);
                    switch (g_marquee_led_index) {
                        case 0: ioctl(g_led_fd, GEC_LED1, LED_CMD_ON); break;
                        case 1: ioctl(g_led_fd, GEC_LED2, LED_CMD_ON); break;
                        case 2: ioctl(g_led_fd, GEC_LED3, LED_CMD_ON); break;
                        case 3: ioctl(g_led_fd, GEC_LED4, LED_CMD_ON); break;
                    }
                    g_marquee_led_index = (g_marquee_led_index + 1) % 4;
                    g_last_marquee_update_time = now;
                }
            }
            break;
        case STATE_AUDIO_PLAYER:
            if (!g_is_paused && g_total_seconds > 0.1) {
                float elapsed_since_resume = (now.tv_sec - g_playback_start_time.tv_sec) + 
                                             (now.tv_usec - g_playback_start_time.tv_usec) / 1000000.0f;
                float total_elapsed = g_current_seconds + elapsed_since_resume;
                if (total_elapsed >= g_total_seconds) {
                    start_audio((g_current_audio_index + 1) % g_audio_count);
                } else {
                    int new_p = (int)((total_elapsed / g_total_seconds) * 100);
                    if (new_p != g_progress_percent) { // 修正：即使new_p > 100也要更新
                        g_progress_percent = new_p > 100 ? 100 : new_p;
                        update_progress_bar_on_screen(g_progress_percent);
                    }
                }
            }
            break;
        default:
            break;
    }

    // 新增：周期性检查语音命令文件
    check_for_voice_command();
}

// 专门处理状态切换和渲染
static void do_state_machine_and_rendering(AppState *last_rendered_state) {
    if (*last_rendered_state == g_app_state) {
        return; // 状态未变，无需渲染
    }

    switch(g_app_state) {
        case STATE_MAIN_MENU:    render_main_menu();       break;
        case STATE_SENSOR_VIEW:  render_sensor_view();     break;
        case STATE_BEEPER_VIEW:  render_beeper_view();     break;
        case STATE_LED_VIEW:     render_led_view();        break;
        case STATE_AUDIO_PLAYER: render_audio_player_view(); break;
        case STATE_AC_VIEW:      render_ac_view();         break;
        case STATE_CAMERA_VIEW:  render_camera_view();     break; //  ：新增
        case STATE_PAINT_VIEW:   render_paint_view();      break;
        default: break;
    }

    if (g_app_state != STATE_EXIT) {
        display_on_lcd();
    }
    
    *last_rendered_state = g_app_state;
}


// 主函数
int main(int argc, char *argv[]) {
    atexit(cleanup_app);
    play_startup_animation();

    //  ：新增的人脸识别流程
    face_recognition_loop();

    if (initialize_app() != 0) {
        return -1;
    }

    printf("正在清空输入缓冲区...\n");
    struct input_event temp_ev;
    int flags = fcntl(g_touch_fd, F_GETFL, 0);
    fcntl(g_touch_fd, F_SETFL, flags | O_NONBLOCK);
    while (read(g_touch_fd, &temp_ev, sizeof(temp_ev)) > 0);
    fcntl(g_touch_fd, F_SETFL, flags);
    printf("输入缓冲区已清空。\n");

    // --- 初始渲染 ---
    AppState last_rendered_state = STATE_EXIT; // 强制进行第一次渲染
    do_state_machine_and_rendering(&last_rendered_state);

    // --- REFACTORED 主事件循环 ---
    while(g_app_state != STATE_EXIT) {
        // --- 优先处理外部应用 ---
        if (g_external_app_pid != -1) {
            int status;
            pid_t result = waitpid(g_external_app_pid, &status, WNOHANG);
            if (result == g_external_app_pid) {
                printf("外部应用 (PID: %d) 已退出。\n", g_external_app_pid);
                g_external_app_pid = -1;
                if (g_audio_paused_for_external_app) {
                    printf("恢复被暂停的背景音频...\n");
                    send_media_cmd("pause\n");
                    g_audio_paused_for_external_app = false;
                }
                // 强制刷新主菜单
                last_rendered_state = STATE_EXIT;
                // 清理触摸缓冲区
                fcntl(g_touch_fd, F_SETFL, O_NONBLOCK);
                while (read(g_touch_fd, &temp_ev, sizeof(temp_ev)) > 0);
                fcntl(g_touch_fd, F_SETFL, flags);
            } else {
                usleep(200000); // 应用运行时，降低CPU占用
                continue;
            }
        }
        
        // --- 分离的逻辑块 ---
        poll_physical_keys(); //  ：暂时禁用，以消除 "No such device" 的错误日志
        do_event_processing();              // 2. I/O 事件 (select, 很快)
        do_periodic_tasks();                // 3. 周期性任务 (检查时间戳，很快)
        do_state_machine_and_rendering(&last_rendered_state); // 4. 渲染 (只在需要时执行，可能很慢)
    }

    return 0;
} 

// =================================================================================
// 13.  : 实体按键处理逻辑
// =================================================================================

static void handle_key_action(int key_index, bool is_long_press) {
    printf("按键事件: 索引=%d (%d), 类型=%s\n", key_index, g_key_gpios[key_index], is_long_press ? "长按" : "短按");

    switch (key_index) {
        case 0: // KEY2 (GPIO 28)
            if (is_long_press) { // 长按: 静音
                printf("  动作: 静音切换\n");
                send_media_cmd("mute\n");
            } else { // 短按: 播放/暂停
                // 修改: 只要有音频在播放，就应该能暂停/恢复，无论在哪一个界面
                if (g_audio_mplayer_pid > 0) {
                    printf("  动作: 播放/暂停\n");
                    send_media_cmd("pause\n");
                    // 注意: g_is_paused 的状态会在主循环中通过计时器等逻辑自动同步，
                    // 这里只负责发送命令。
                }
            }
            break;

        case 1: // KEY3 (GPIO 62)
            if (is_long_press) { // 长按: 上一曲
                // 修改: 只要有音频在播放，就应该能切歌
                if (g_audio_mplayer_pid > 0 && g_audio_count > 0) {
                     printf("  动作: 上一曲\n");
                     start_audio((g_current_audio_index - 1 + g_audio_count) % g_audio_count);
                }
            } else { // 短按: 音量减
                printf("  动作: 音量减\n");
                send_media_cmd("volume -5\n");
            }
            break;

        case 2: // KEY4 (GPIO 63)
            if (is_long_press) { // 长按: 下一曲
                // 修改: 只要有音频在播放，就应该能切歌
                if (g_audio_mplayer_pid > 0 && g_audio_count > 0) {
                     printf("  动作: 下一曲\n");
                     start_audio((g_current_audio_index + 1) % g_audio_count);
                }
            } else { // 短按: 音量加
                printf("  动作: 音量加\n");
                send_media_cmd("volume +5\n");
            }
            break;
        
        case 3: // KEY6 (GPIO 41)
            // 对于 KEY6, 长按和短按最终都返回主菜单，因此逻辑合并处理
            if (is_long_press) {
                printf("  动作: 返回主页\n");
            } else {
                printf("  动作: 返回\n");
            }
            
            // 修改: 优先处理外部应用
            if (g_external_app_pid != -1) {
                printf("  (操作: 正在关闭外部应用 PID: %d...)\n", g_external_app_pid);
                kill(g_external_app_pid, SIGKILL);
                // waitpid 将在主循环的开头处理后续事宜
                break; // 直接结束本次按键处理
            }

            if (g_app_state != STATE_MAIN_MENU) {
                // 在改变状态前，执行特定于当前状态的清理工作
                switch (g_app_state) {
                    case STATE_AUDIO_PLAYER:
                        printf("  (清理: 正在停止背景视频...)\n");
                        stop_video();
                        break;
                    case STATE_BEEPER_VIEW:
                        printf("  (清理: 正在关闭蜂鸣器...)\n");
                        if (g_beep_fd != -1) {
                            set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
                            ioctl(g_beep_fd, BEEP_OFF, 1);
                        }
                        break;
                    case STATE_LED_VIEW:
                        printf("  (清理: 正在关闭LED...)\n");
                        g_is_marquee_running = false;
                        if (g_led_fd != -1) {
                            ioctl(g_led_fd, GEC_LED1, LED_CMD_OFF);
                            ioctl(g_led_fd, GEC_LED2, LED_CMD_OFF);
                            ioctl(g_led_fd, GEC_LED3, LED_CMD_OFF);
                            ioctl(g_led_fd, GEC_LED4, LED_CMD_OFF);
                        }
                        break;
                    case STATE_PAINT_VIEW:
                        printf("  (清理: 正在保存画布并退出...)\n");
                        if (save_canvas_to_bmp("/root/bmpdk") == 0) {
                            printf("  画布保存成功!\n");
                        } else {
                            printf("  错误: 画布保存失败。\n");
                        }
                        break;
                    default:
                        // 其他状态如传感器、空调界面没有需要特殊清理的后台任务
                        break;
                }

                // 执行完所有清理后，再安全地切换到主菜单状态
                g_app_state = STATE_MAIN_MENU;
                //  ：立即渲染并显示主菜单，确保即时响应
                render_main_menu();
                display_on_lcd();
            }
            break;
    }
}


static void poll_physical_keys(void) {
    // 检查是否禁用GPIO访问
    char *disable_gpio = getenv("DISABLE_GPIO");
    if (disable_gpio && strcmp(disable_gpio, "1") == 0) {
        // GPIO访问被禁用，直接返回
        return;
    }

    char value_char;
    struct timeval now;
    gettimeofday(&now, NULL); // 在循环开始时获取一次时间，供所有按键使用

    for (int i = 0; i < NUM_KEYS; i++) {
        if (g_key_fds[i] == -1) continue;

        // lseek + read 是读取 sysfs 'value' 文件的标准方法
        lseek(g_key_fds[i], 0, SEEK_SET);
        if (read(g_key_fds[i], &value_char, 1) != 1) {
            perror("读取 GPIO value 失败");
            continue;
        }

        bool is_pressed_level = (value_char != g_key_initial_state[i]);
        long elapsed_us;

        switch (g_key_state[i]) {
            case KEY_STATE_IDLE:
                if (is_pressed_level) {
                    // 电平变化，进入"按下防抖"状态
                    g_key_state[i] = KEY_STATE_DEBOUNCING_PRESS;
                    g_key_debounce_start_time[i] = now;
                }
                break;

            case KEY_STATE_DEBOUNCING_PRESS:
                elapsed_us = (now.tv_sec - g_key_debounce_start_time[i].tv_sec) * 1000000L + (now.tv_usec - g_key_debounce_start_time[i].tv_usec);
                if (!is_pressed_level) {
                    // 电平在防抖期间跳回去了，说明是干扰，返回IDLE
                    g_key_state[i] = KEY_STATE_IDLE;
                } else if (elapsed_us >= DEBOUNCE_DURATION_US) {
                    // 电平在防抖期间保持稳定，确认是有效按下
                    g_key_state[i] = KEY_STATE_PRESSED;
                    g_key_long_press_triggered[i] = false;
                    g_key_press_start_time[i] = now; // 从此刻开始计算长按
                }
                break;

            case KEY_STATE_PRESSED:
                if (!is_pressed_level) {
                    // 电平变为释放，进入"释放防抖"状态
                    g_key_state[i] = KEY_STATE_DEBOUNCING_RELEASE;
                    g_key_debounce_start_time[i] = now;
                } else {
                    // 仍然被按住，检查是否满足长按条件
                    if (!g_key_long_press_triggered[i]) {
                        elapsed_us = (now.tv_sec - g_key_press_start_time[i].tv_sec) * 1000000L + (now.tv_usec - g_key_press_start_time[i].tv_usec);
                        if (elapsed_us >= LONG_PRESS_DURATION_US) {
                            handle_key_action(i, true); // 触发长按
                            g_key_long_press_triggered[i] = true; // 标记已触发，防止重复
                        }
                    }
                }
                break;

            case KEY_STATE_DEBOUNCING_RELEASE:
                elapsed_us = (now.tv_sec - g_key_debounce_start_time[i].tv_sec) * 1000000L + (now.tv_usec - g_key_debounce_start_time[i].tv_usec);
                if (is_pressed_level) {
                    // 电平在防抖期间跳回去了，说明误判，返回PRESSED
                    g_key_state[i] = KEY_STATE_PRESSED;
                } else if (elapsed_us >= DEBOUNCE_DURATION_US) {
                    // 电平在防抖期间保持稳定，确认是有效释放
                    g_key_state[i] = KEY_STATE_IDLE;
                    if (!g_key_long_press_triggered[i]) {
                        // 如果长按没被触发过，那么这就是一次短按
                        handle_key_action(i, false);
                    }
                }
                break;
        }
    }
} 

// =================================================================================
// 15. 人脸识别功能函数
// =================================================================================

/**
 * @brief 将指定路径的文件通过TCP发送到预设的虚拟机。
 * @param filepath 要发送的文件的完整路径。
 */
static void send_image_to_vm(const char* filepath) {
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("错误: 无法创建套接字 (VM)");
        return;
    }

    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(VM_PORT);
    if (inet_pton(AF_INET, VM_IP, &server_addr.sin_addr) <= 0) {
        perror("错误: 无效的IP地址或地址族 (VM)");
        close(sockfd);
        return;
    }

    printf("正在连接到虚拟机 %s:%d...\n", VM_IP, VM_PORT);
    if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("错误: 连接到虚拟机失败");
        close(sockfd);
        return;
    }

    printf("连接成功。正在发送文件: %s\n", filepath);

    int fd = open(filepath, O_RDONLY);
    if (fd == -1) {
        perror("错误: 无法打开BMP文件进行发送");
        close(sockfd);
        return;
    }

    char buffer[4096];
    ssize_t bytes_read;
    while ((bytes_read = read(fd, buffer, sizeof(buffer))) > 0) {
        if (write(sockfd, buffer, bytes_read) != bytes_read) {
            perror("错误: 发送文件失败");
            break;
        }
    }

    if (bytes_read < 0) {
        perror("错误: 读取文件进行发送时失败");
    }

    close(fd);
    close(sockfd);
    printf("文件发送完毕，连接已关闭。\n");
}

/**
 * @brief 捕获整个framebuffer的内容并保存为24位BMP文件。
 * @param filepath 保存BMP文件的完整路径。
 * @return 成功返回0，失败返回-1。
 */
static int capture_screen_to_bmp(const char* filepath) {
    const int width = 800;
    const int height = 480;
    const int bpp = 4;
    const int screen_size = width * height * bpp;

    int fb_fd = open("/dev/fb0", O_RDONLY);
    if (fb_fd == -1) {
        perror("错误: 无法打开framebuffer设备 /dev/fb0");
        return -1;
    }

    void* fb_mem = mmap(NULL, screen_size, PROT_READ, MAP_SHARED, fb_fd, 0);
    if (fb_mem == MAP_FAILED) {
        perror("错误: 映射framebuffer内存失败");
        close(fb_fd);
        return -1;
    }

    FILE* f = fopen(filepath, "wb");
    if (!f) {
        perror("错误: 无法创建BMP文件");
        munmap(fb_mem, screen_size);
        close(fb_fd);
        return -1;
    }

    int row_padded = (width * 3 + 3) & ~3;
    int image_size = row_padded * height;

    BMPFileHeader fh;
    fh.signature[0] = 'B';
    fh.signature[1] = 'M';
    fh.fileSize = 54 + image_size;
    fh.reserved1 = 0;
    fh.reserved2 = 0;
    fh.dataOffset = 54;

    BMPInfoHeader fih;
    fih.size = sizeof(BMPInfoHeader);
    fih.width = width;
    fih.height = height;
    fih.planes = 1;
    fih.bitCount = 24;
    fih.compression = 0;
    fih.imageSize = image_size;
    fih.xPixelsPerMeter = 0;
    fih.yPixelsPerMeter = 0;
    fih.colorsUsed = 0;
    fih.colorsImportant = 0;

    fwrite(&fh, sizeof(fh), 1, f);
    fwrite(&fih, sizeof(fih), 1, f);

    unsigned char* row_data = (unsigned char*)malloc(row_padded);
    int* screen_buffer = (int*)fb_mem;
    int x, y;

    for (y = height - 1; y >= 0; y--) {
        for (x = 0; x < width; x++) {
            int pixel_color = screen_buffer[y * width + x];
            row_data[x * 3] = pixel_color & 0xFF;
            row_data[x * 3 + 1] = (pixel_color >> 8) & 0xFF;
            row_data[x * 3 + 2] = (pixel_color >> 16) & 0xFF;
        }
        fwrite(row_data, 1, row_padded, f);
    }

    free(row_data);
    fclose(f);
    munmap(fb_mem, screen_size);
    close(fb_fd);

    return 0;
}


/**
 * @brief 检查人脸识别结果文件，判断是否识别成功。
 * @return 如果最新的识别结果不是"Unknown"则返回true，否则返回false。
 */
static bool check_local_recognition_result(void) {
    FILE *f = fopen(RECOGNITION_RESULT_PATH, "r");
    if (!f) {
        return false;
    }

    char last_line[256] = {0};
    char current_line[256];
    while (fgets(current_line, sizeof(current_line), f)) {
        current_line[strcspn(current_line, "\r\n")] = 0;
        if (strlen(current_line) > 0) {
            strcpy(last_line, current_line);
        }
    }
    fclose(f);

    if (strlen(last_line) > 0 && strcmp(last_line, "Unknown") != 0) {
        printf("识别成功，结果: %s\n", last_line);
        return true;
    }
    
    return false;
}

/**
 * @brief 人脸识别主循环（开发板端）- 最终同步版
 */
static void face_recognition_loop(void) {
    // 在循环开始前，清空旧的识别结果文件
    FILE* f_clear = fopen(RECOGNITION_RESULT_PATH, "w");
    if (f_clear) {
        fclose(f_clear);
        printf("旧的识别结果文件 %s 已清空。\n", RECOGNITION_RESULT_PATH);
    } else {
        // 如果文件不存在或无法打开，这可能不是一个致命错误，
        // 但记录下来有助于调试。
        perror("警告：无法清空识别结果文件");
    }

    pid_t camera_pid = -1;

    // 1. 启动mplayer以显示摄像头画面
    camera_pid = fork();
    if (camera_pid == 0) { // 子进程
        execlp("mplayer", "mplayer", "tv://",
               "-tv", "driver=v4l2:device=/dev/video7:width=800:height=480:fps=15",
               "-vo", "fbdev2:/dev/fb0", "-vf", "scale=800:480,mirror",
               "-nosound", "-quiet", NULL);
        perror("execlp 启动摄像头 mplayer 失败");
        exit(1);
    } else if (camera_pid < 0) {
        perror("fork 摄像头 mplayer 失败");
        return;
    }

    printf("人脸识别流程已启动（最终同步版）。\n");
    sleep(2);

    while (1) {
        if (check_local_recognition_result()) {
            printf("检测到成功结果，退出识别循环。\n");
            break;
        }

        printf("正在拍照并发送...\n");
        if (capture_screen_to_bmp(TEMP_BMP_PATH) != 0) {
            fprintf(stderr, "错误: 捕获屏幕失败，1秒后重试。\n");
            sleep(1);
            continue;
        }

        int sockfd = socket(AF_INET, SOCK_STREAM, 0);
        if (sockfd < 0) {
            perror("创建套接字失败，1秒后重试");
            sleep(1);
            continue;
        }

        struct sockaddr_in vm_addr;
        memset(&vm_addr, 0, sizeof(vm_addr));
        vm_addr.sin_family = AF_INET;
        vm_addr.sin_port = htons(VM_PORT);
        inet_pton(AF_INET, VM_IP, &vm_addr.sin_addr);

        if (connect(sockfd, (struct sockaddr*)&vm_addr, sizeof(vm_addr)) < 0) {
            perror("连接虚拟机失败，1秒后重试");
            close(sockfd);
            sleep(1);
            continue;
        }

        // 发送图片
        int img_fd = open(TEMP_BMP_PATH, O_RDONLY);
        if (img_fd >= 0) {
            char file_buf[4096];
            ssize_t n;
            while ((n = read(img_fd, file_buf, sizeof(file_buf))) > 0) {
                if (write(sockfd, file_buf, n) != n) {
                    perror("发送文件时出错");
                    break;
                }
            }
            close(img_fd);
        }
        // 发送完毕后，关闭写通道，告知服务器"我发完了"
        shutdown(sockfd, SHUT_WR);

        // 接收结果
        char result_buf[256] = {0};
        int bytes_read = read(sockfd, result_buf, sizeof(result_buf) - 1);
        close(sockfd); // 无论如何都关闭连接

        if (bytes_read > 0) {
            printf("从虚拟机收到结果: '%s'\n", result_buf);
            FILE* f = fopen(RECOGNITION_RESULT_PATH, "w");
            if (f) {
                fwrite(result_buf, 1, bytes_read, f);
                fclose(f);
                printf("结果已写入 %s\n", RECOGNITION_RESULT_PATH);
            } else {
                perror("写入本地结果文件失败");
            }
        } else if (bytes_read < 0) {
            perror("从虚拟机接收结果失败");
        }
    }

    // 清理工作
    if (camera_pid > 0) {
        kill(camera_pid, SIGKILL);
        waitpid(camera_pid, NULL, 0);
    }
    remove(TEMP_BMP_PATH);
    printf("人脸识别流程结束。\n");
} 

// 新增：检查并处理语音命令文件的函数
static void check_for_voice_command(void) {
    const char* command_filepath = "/tmp/voice_command.txt";
    // access() 函数检查文件是否存在且可读
    if (access(command_filepath, F_OK) == 0) {
        FILE* cmd_file = fopen(command_filepath, "r");
        if (cmd_file) {
            char command_buffer[256] = {0};
            if (fgets(command_buffer, sizeof(command_buffer), cmd_file)) {
                // 移除末尾的换行符
                command_buffer[strcspn(command_buffer, "\r\n")] = 0;
                printf("检测到并读取语音命令: '%s'\n", command_buffer);
                handle_voice_command(command_buffer);
            }
            fclose(cmd_file);
        }
        
        // 处理完毕后，立即删除文件，避免重复执行
        if (remove(command_filepath) != 0) {
            perror("警告: 删除命令文件失败");
        }
    }
}

// 新增：根据命令字符串执行具体操作的函数
static void handle_voice_command(const char* command) {
    if (g_external_app_pid != -1) {
        printf("忽略语音命令，因为已有外部应用在运行。\n");
        return;
    }

    printf("正在处理语音命令: %s\n", command);

    // 在执行任何新命令前，先清理当前界面的资源
    // 这样可以确保无论在哪个界面接收到语音命令，都能安全地切换到新界面
    switch (g_app_state) {
        case STATE_CAMERA_VIEW:
            // 停止摄像头预览
            if (g_camera_view_pid > 0) {
                printf("清理: 关闭摄像头预览\n");
                kill(g_camera_view_pid, SIGKILL);
                waitpid(g_camera_view_pid, NULL, 0);
                g_camera_view_pid = -1;
            }
            break;
        case STATE_BEEPER_VIEW:
            // 关闭蜂鸣器
            if (g_beep_fd != -1) {
                printf("清理: 关闭蜂鸣器\n");
                set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
                ioctl(g_beep_fd, BEEP_OFF, 1);
            }
            break;
        case STATE_LED_VIEW:
            // 停止流水灯并关闭所有LED
            printf("清理: 关闭LED\n");
            g_is_marquee_running = false;
            if (g_led_fd != -1) {
                ioctl(g_led_fd, GEC_LED1, LED_CMD_OFF);
                ioctl(g_led_fd, GEC_LED2, LED_CMD_OFF);
                ioctl(g_led_fd, GEC_LED3, LED_CMD_OFF);
                ioctl(g_led_fd, GEC_LED4, LED_CMD_OFF);
            }
            break;
        case STATE_AUDIO_PLAYER:
            // 如果要切换到其他界面，停止视频播放
            // 注意：不停止音频播放，让它在后台继续
            if (strcmp(command, "CMD_OPEN_AUDIO") != 0) {
                printf("清理: 停止背景视频\n");
                stop_video();
            }
            break;
        case STATE_PAINT_VIEW:
            // 如果用户正在绘图，可以考虑自动保存画布
            if (g_is_confirming_exit || g_is_drawing || g_is_selecting_color) {
                printf("清理: 保存画布\n");
                save_canvas_to_bmp("/root/bmpdk");
            }
            break;
        default:
            // 其他界面不需要特殊清理
            break;
    }

    if (strcmp(command, "CMD_OPEN_ALBUM") == 0) {
        printf("执行语音命令: 打开相册\n");
        // 直接切换到相册状态，而不是启动外部程序
        // 这里可以添加相册相关的初始化代码
        
        // 如果有音频正在播放，可以考虑暂停
        if (g_audio_mplayer_pid > 0 && !g_is_paused) {
            printf("暂停背景音频以打开相册。\n");
            send_media_cmd("pause\n");
            g_audio_paused_for_external_app = true;
        }
        
        // 启动相册应用
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程：执行album_app程序（如果存在）
            execlp("./album_app", "album_app", NULL);
            // 如果execlp失败，则输出错误信息并退出
            perror("execlp album_app failed");
            exit(1);
        } else if (pid > 0) {
            // 父进程：记录子进程ID
            g_external_app_pid = pid;
        }
    } else if (strcmp(command, "CMD_OPEN_THUMBNAIL") == 0) {
        printf("执行语音命令: 打开缩略图视图\n");
        // 如果有音频正在播放，可以考虑暂停
        if (g_audio_mplayer_pid > 0 && !g_is_paused) {
            printf("暂停背景音频以打开相册缩略图。\n");
            send_media_cmd("pause\n");
            g_audio_paused_for_external_app = true;
        }
        
        // 启动相册应用，带缩略图参数
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程：执行album_app程序，带缩略图参数
            execlp("./album_app", "album_app", "--thumbnail", NULL);
            perror("execlp album_app --thumbnail failed");
            exit(1);
        } else if (pid > 0) {
            // 父进程：记录子进程ID
            g_external_app_pid = pid;
        }
    } else if (strcmp(command, "CMD_OPEN_SLIDESHOW") == 0) {
        printf("执行语音命令: 打开幻灯片播放\n");
        // 如果有音频正在播放，可以考虑暂停
        if (g_audio_mplayer_pid > 0 && !g_is_paused) {
            printf("暂停背景音频以播放幻灯片。\n");
            send_media_cmd("pause\n");
            g_audio_paused_for_external_app = true;
        }
        
        // 启动相册应用，带幻灯片参数
        pid_t pid = fork();
        if (pid == 0) {
            // 子进程：执行album_app程序，带幻灯片参数
            execlp("./album_app", "album_app", "--slideshow", NULL);
            perror("execlp album_app --slideshow failed");
            exit(1);
        } else if (pid > 0) {
            // 父进程：记录子进程ID
            g_external_app_pid = pid;
        }
    } else if (strcmp(command, "CMD_OPEN_VIDEO") == 0) {
        printf("执行语音命令: 打开视频\n");
        // 如果音频正在播放，则暂停它
        if (g_audio_mplayer_pid > 0 && !g_is_paused) {
            printf("暂停背景音频以播放视频。\n");
            send_media_cmd("pause\n");
            g_audio_paused_for_external_app = true;
        }
        
        pid_t pid = fork();
        if (pid == 0) {
            execlp("./video_app", "video_app", NULL);
            perror("execlp video_app failed");
            exit(1);
        } else if (pid > 0) {
            g_external_app_pid = pid;
        }
    } else if (strcmp(command, "CMD_OPEN_AUDIO") == 0) {
        printf("执行语音命令: 切换到音乐播放器\n");
        g_app_state = STATE_AUDIO_PLAYER;
        start_video(); // 进入界面时，启动视频
        // 如果当前没有任何音乐在播放，则自动播放第一首
        if (g_audio_mplayer_pid <= 0 && g_audio_count > 0) {
            start_audio(0);
        }
    } else if (strcmp(command, "CMD_OPEN_PAINT") == 0) {
        printf("执行语音命令: 切换到绘画界面\n");
        g_app_state = STATE_PAINT_VIEW;
    } else if (strcmp(command, "CMD_OPEN_BEEPER") == 0) {
        printf("执行语音命令: 切换到蜂鸣器界面并打开蜂鸣器\n");
        g_app_state = STATE_BEEPER_VIEW;
        
        // 打开蜂鸣器
        if (g_beep_fd != -1) {
            ioctl(g_beep_fd, BEEP_ON, 1);
            // 如果当前是静音，则自动设置为一个中等音量
            if (g_current_volume_level == 0) {
                g_current_volume_level = 3; // 设为中等音量
            }
            int current_duty_cycle = g_volume_levels[g_current_volume_level];
            set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, current_duty_cycle);
        }
    } else if (strcmp(command, "CMD_OPEN_AC") == 0) {
        printf("执行语音命令: 切换到空调界面并打开空调\n");
        g_app_state = STATE_AC_VIEW;
        
        // 打开空调（播放开启提示音）
        if (g_beep_fd != -1) {
            // 为按键提示音定义一个固定的音量（50%），避免受蜂鸣器界面音量设置的影响
            const int click_sound_duty_cycle = 500000;
            
            // 播放空调开启提示音
            ioctl(g_beep_fd, BEEP_ON, 1);
            set_pwm_params(PWM_CONTROL_DEVICE, PWM_PERIOD, click_sound_duty_cycle);
            usleep(300000); // 响 0.3 秒
            set_pwm_params(PWM_CONTROL_DEVICE, 0, 0);
            ioctl(g_beep_fd, BEEP_OFF, 1);
        }
    } else if (strcmp(command, "CMD_OPEN_SENSOR") == 0) {
        printf("执行语音命令: 切换到温湿度界面\n");
        g_app_state = STATE_SENSOR_VIEW;
    } else if (strcmp(command, "CMD_OPEN_LED") == 0) {
        printf("执行语音命令: 切换到LED界面并打开所有LED\n");
        g_app_state = STATE_LED_VIEW;
        
        // 打开所有LED
        if (g_led_fd != -1) {
            ioctl(g_led_fd, GEC_LED1, LED_CMD_ON);
            ioctl(g_led_fd, GEC_LED2, LED_CMD_ON);
            ioctl(g_led_fd, GEC_LED3, LED_CMD_ON);
            ioctl(g_led_fd, GEC_LED4, LED_CMD_ON);
        }
    } else if (strcmp(command, "CMD_OPEN_CAMERA") == 0 || strcmp(command, "CMD_CAMERA") == 0) {
        // 新增对摄像头的语音命令支持
        printf("执行语音命令: 打开摄像头预览\n");
        g_app_state = STATE_CAMERA_VIEW;
    } else if (strncmp(command, "CMD_DIAL:", 9) == 0) {
        const char* contact_name = command + 9;
        // 从联系人姓名后截断可能存在的"的电话"等的多余词语
        char clean_name[64] = {0};
        strncpy(clean_name, contact_name, sizeof(clean_name) - 1);
        char* suffix_ptr = strstr(clean_name, "的");
        if(suffix_ptr) {
            *suffix_ptr = '\0';
        }
        printf("执行语音命令: 打电话给 %s (功能待实现)\n", clean_name);
        // 此处可以添加实际的打电话逻辑
    } else if (strcmp(command, "CMD_UNKNOWN") == 0) {
        printf("收到未知或无法识别的语音命令。\n");
    } else {
        printf("收到未处理的语音命令: '%s'\n", command);
    }
    
    // 在命令处理完成后，立即渲染新界面
    if (g_app_state != STATE_EXIT && g_external_app_pid == -1) {
        switch(g_app_state) {
            case STATE_MAIN_MENU:    render_main_menu();       break;
            case STATE_SENSOR_VIEW:  render_sensor_view();     break;
            case STATE_BEEPER_VIEW:  render_beeper_view();     break;
            case STATE_LED_VIEW:     render_led_view();        break;
            case STATE_AUDIO_PLAYER: render_audio_player_view(); break;
            case STATE_AC_VIEW:      render_ac_view();         break;
            case STATE_CAMERA_VIEW:  render_camera_view();     break;
            case STATE_PAINT_VIEW:   render_paint_view();      break;
            default: break;
        }
        display_on_lcd();
    }
}

//  ：新增：处理摄像头预览界面的触摸事件

// 绘制语音识别按钮
void draw_voice_button(int* buffer) {
    // 不再绘制色块，使按钮在视觉上不可见
    // 但保留功能区域在右上角 60x60 的位置
    
    // 注释掉以下代码，不再绘制橙色背景
    /*
    for (int y = g_rect_voice_client_trigger.y; y < g_rect_voice_client_trigger.y + g_rect_voice_client_trigger.height; y++) {
        for (int x = g_rect_voice_client_trigger.x; x < g_rect_voice_client_trigger.x + g_rect_voice_client_trigger.width; x++) {
            buffer[y * LCD_WIDTH + x] = 0x00FF9900; // 橙色
        }
    }
    // 在方块上绘制"语音"文字
    draw_text(buffer, g_rect_voice_client_trigger.x + 10, g_rect_voice_client_trigger.y + 20, "语音", 0x00000000);
    */
    
    // 函数保留为空，只是为了维持接口一致性
}