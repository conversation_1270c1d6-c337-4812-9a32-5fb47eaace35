#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>
#include <errno.h>
#include <sys/stat.h> // 用于 mkdir

#include "../../include/qisr.h"
#include "../../include/msp_cmn.h"
#include "../../include/msp_errors.h"

#define SAMPLE_RATE_16K     (16000)
#define MAX_GRAMMARID_LEN   (32)
#define MAX_PARAMS_LEN      (1024)
#define BUFFER_SIZE         1024

// 讯飞SDK相关配置
const char * ASR_RES_PATH        = "fo|res/asr/common.jet"; 
const char * GRM_BUILD_PATH      = "res/asr/GrmBuilld";
const char * GRM_FILE            = "call.bnf"; 

typedef struct _UserData {
    int     build_fini; 
    int     update_fini;
    int     errcode; 
    char    grammar_id[MAX_GRAMMARID_LEN]; 
} UserData;

// 函数声明
int build_grammar(UserData *udata);
char* run_asr_on_file(UserData *udata, const char *audio_file);
// wav_to_pcm 函数不再需要，已被删除


int build_grm_cb(int ecode, const char *info, void *udata)
{
	UserData *grm_data = (UserData *)udata;

	if (NULL != grm_data) {
		grm_data->build_fini = 1;
		grm_data->errcode = ecode;
	}

	if (MSP_SUCCESS == ecode && NULL != info) {
		printf("构建语法成功！ 语法ID:%s\n", info);
		if (NULL != grm_data)
			snprintf(grm_data->grammar_id, MAX_GRAMMARID_LEN - 1, info);
	}
	else
		printf("构建语法失败！%d\n", ecode);

	return 0;
}

int build_grammar(UserData *udata)
{
	FILE *grm_file                           = NULL;
	char *grm_content                        = NULL;
	unsigned int grm_cnt_len                 = 0;
	char grm_build_params[MAX_PARAMS_LEN]    = {NULL};
	int ret                                  = 0;

	grm_file = fopen(GRM_FILE, "rb");	
	if(NULL == grm_file) {
		printf("打开\"%s\"文件失败！[%s]\n", GRM_FILE, strerror(errno));
		return -1; 
	}

	fseek(grm_file, 0, SEEK_END);
	grm_cnt_len = ftell(grm_file);
	fseek(grm_file, 0, SEEK_SET);

	grm_content = (char *)malloc(grm_cnt_len + 1);
	if (NULL == grm_content)
	{
		printf("内存分配失败!\n");
		fclose(grm_file);
		grm_file = NULL;
		return -1;
	}
	fread((void*)grm_content, 1, grm_cnt_len, grm_file);
	grm_content[grm_cnt_len] = '\0';
	fclose(grm_file);
	grm_file = NULL;

	snprintf(grm_build_params, MAX_PARAMS_LEN - 1, 
		"engine_type = local, \
		asr_res_path = %s, sample_rate = %d, \
		grm_build_path = %s, ",
		ASR_RES_PATH,
		SAMPLE_RATE_16K,
		GRM_BUILD_PATH
		);
	ret = QISRBuildGrammar("bnf", grm_content, grm_cnt_len, grm_build_params, build_grm_cb, udata);

	free(grm_content);
	grm_content = NULL;

	return ret;
}

char* run_asr_on_file(UserData *udata, const char *audio_file)
{
	char asr_params[MAX_PARAMS_LEN]    = {NULL};
	const char *rec_rslt               = NULL;
	const char *session_id             = NULL;
	FILE *f_pcm                        = NULL;
	char *pcm_data                     = NULL;
	long pcm_count                     = 0;
	long pcm_size                      = 0;
	int last_audio                     = 0;
	int aud_stat                       = MSP_AUDIO_SAMPLE_CONTINUE;
	int ep_status                      = MSP_EP_LOOKING_FOR_SPEECH;
	int rec_status                     = MSP_REC_STATUS_INCOMPLETE;
	int rss_status                     = MSP_REC_STATUS_INCOMPLETE;
	int errcode                        = -1;
	char* result_to_return             = NULL;

	f_pcm = fopen(audio_file, "rb");
	if (NULL == f_pcm) {
		printf("打开\"%s\"失败！[%s]\n", audio_file, strerror(errno));
		goto run_error;
	}
	fseek(f_pcm, 0, SEEK_END);
	pcm_size = ftell(f_pcm);
	fseek(f_pcm, 0, SEEK_SET);
	pcm_data = (char *)malloc(pcm_size);
	if (NULL == pcm_data)
		goto run_error;
	fread((void *)pcm_data, pcm_size, 1, f_pcm);
	fclose(f_pcm);
	f_pcm = NULL;

	snprintf(asr_params, MAX_PARAMS_LEN - 1, 
		"engine_type = local, \
		asr_res_path = %s, sample_rate = %d, \
		grm_build_path = %s, local_grammar = %s, \
		result_type = xml, result_encoding = UTF-8, ",
		ASR_RES_PATH,
		SAMPLE_RATE_16K,
		GRM_BUILD_PATH,
		udata->grammar_id
		);
	session_id = QISRSessionBegin(NULL, asr_params, &errcode);
	if (NULL == session_id)
		goto run_error;
	printf("开始识别...\n");

	while (1) {
		unsigned int len = 6400;

		if (pcm_size < 12800) {
			len = pcm_size;
			last_audio = 1;
		}
		aud_stat = MSP_AUDIO_SAMPLE_CONTINUE;
		if (0 == pcm_count)
			aud_stat = MSP_AUDIO_SAMPLE_FIRST;
		if (len <= 0)
			break;

		printf(">");
		fflush(stdout);
		errcode = QISRAudioWrite(session_id, (const void *)&pcm_data[pcm_count], len, aud_stat, &ep_status, &rec_status);
		if (MSP_SUCCESS != errcode)
			goto run_error;
		pcm_count += (long)len;
		pcm_size -= (long)len;
		if (MSP_EP_AFTER_SPEECH == ep_status)
			break;
		usleep(150 * 1000); 
	}
	QISRAudioWrite(session_id, (const void *)NULL, 0, MSP_AUDIO_SAMPLE_LAST, &ep_status, &rec_status);

	free(pcm_data);
	pcm_data = NULL;

	while (MSP_REC_STATUS_COMPLETE != rss_status && MSP_SUCCESS == errcode) {
		rec_rslt = QISRGetResult(session_id, &rss_status, 0, &errcode);
		usleep(150 * 1000);
	}
	printf("\n识别结束，原始XML结果：\n");
	printf("=============================================================\n");
	if (NULL != rec_rslt) {
		printf("%s\n", rec_rslt);
		
        // --- 开始解析XML并映射到命令ID ---
        char raw_text[256] = {0};
        char* p_start = strstr(rec_rslt, "<rawtext>");
        char* p_end = strstr(rec_rslt, "</rawtext>");

        if (p_start && p_end) {
            p_start += strlen("<rawtext>");
            strncpy(raw_text, p_start, p_end - p_start);
            printf("解析出的用户原文: %s\n", raw_text);

            // 根据用户原文的关键字映射命令ID
            // 注意：这里的if-else if顺序很重要，更具体的命令应该放在前面
            if (strstr(raw_text, "打电话给")) {
                char* name_start = strstr(raw_text, "打电话给") + strlen("打电话给");
                // 简单的处理，可能需要根据实际情况调整以去除末尾词语
                char command_buffer[128] = {0};
                snprintf(command_buffer, sizeof(command_buffer), "CMD_DIAL:%s", name_start);
				result_to_return = strdup(command_buffer);
            } else if (strstr(raw_text, "相册")) {
                result_to_return = strdup("CMD_OPEN_ALBUM");
            } else if (strstr(raw_text, "视频")) {
                result_to_return = strdup("CMD_OPEN_VIDEO");
            } else if (strstr(raw_text, "音乐")) {
                result_to_return = strdup("CMD_OPEN_AUDIO");
            } else if (strstr(raw_text, "绘画")) {
                result_to_return = strdup("CMD_OPEN_PAINT");
            } else if (strstr(raw_text, "蜂鸣器")) {
                result_to_return = strdup("CMD_OPEN_BEEPER");
            } else if (strstr(raw_text, "空调")) {
                result_to_return = strdup("CMD_OPEN_AC");
            } else if (strstr(raw_text, "温湿度")) {
                result_to_return = strdup("CMD_OPEN_SENSOR");
            } else if (strstr(raw_text, "LED") || strstr(raw_text, "灯")) {
                result_to_return = strdup("CMD_OPEN_LED");
            } else if (strstr(raw_text, "摄像头") || strstr(raw_text, "相机") || strstr(raw_text, "拍照")) {
                result_to_return = strdup("CMD_OPEN_CAMERA");
            } else if (strstr(raw_text, "缩略图")) {
                result_to_return = strdup("CMD_OPEN_THUMBNAIL");
            } else if (strstr(raw_text, "幻灯片") || strstr(raw_text, "播放图片")) {
                result_to_return = strdup("CMD_OPEN_SLIDESHOW");
            }
            // 更多 else if ...
            else {
                // 如果所有关键字都未匹配，则为未知命令
                result_to_return = strdup("CMD_UNKNOWN");
            }

        } else {
             // 如果连 <rawtext> 标签都找不到，也视为未知
             result_to_return = strdup("CMD_UNKNOWN");
        }
        // --- 解析和映射结束 ---

	} else {
		printf("没有识别结果！\n");
		result_to_return = strdup("CMD_UNKNOWN");
	}
	printf("=============================================================\n");

	goto run_exit;

run_error:
	if (NULL != pcm_data) {
		free(pcm_data);
		pcm_data = NULL;
	}
	if (NULL != f_pcm) {
		fclose(f_pcm);
		f_pcm = NULL;
	}
run_exit:
	QISRSessionEnd(session_id, NULL);
	return result_to_return;
}

int main(int argc, char* argv[])
{
    // --- 初始化讯飞SDK ---
    const char *login_config    = "appid = 9401cb75";
	UserData asr_data; 
	int ret = MSPLogin(NULL, NULL, login_config);
	if (MSP_SUCCESS != ret) {
		printf("登录失败：%d\n", ret);
		return 1;
	}
    memset(&asr_data, 0, sizeof(UserData));
	printf("构建离线识别语法网络...\n");
	ret = build_grammar(&asr_data);
	if (MSP_SUCCESS != ret) {
		printf("构建语法调用失败！\n");
		goto exit;
	}
	while (1 != asr_data.build_fini)
		usleep(300 * 1000);
	if (MSP_SUCCESS != asr_data.errcode)
		goto exit;
	printf("离线识别语法网络构建完成!\n");	
    // --- 讯飞SDK初始化完毕 ---


    // --- 服务器网络设置 ---
    signal(SIGPIPE, SIG_IGN);

    int server_sockfd, client_sockfd;
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_len;

    server_sockfd = socket(AF_INET, SOCK_STREAM, 0);
    
    int on = 1;
    setsockopt(server_sockfd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));

    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(54321);

    if (bind(server_sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind failed");
        goto exit;
    }
    
    listen(server_sockfd, 5);
    printf("服务器启动成功，将永久运行并等待连接...\n");
    
    // 确保 wav 目录存在
    mkdir("wav", 0755); 
    // --- 服务器网络设置完毕 ---

    static int audio_file_counter = 1; // 用于文件命名的静态计数器

    // --- 主循环，等待客户端连接和处理 ---
    while (1) {
        client_len = sizeof(client_addr);
        client_sockfd = accept(server_sockfd, (struct sockaddr *)&client_addr, &client_len);
        if (client_sockfd < 0) {
            perror("accept failed");
            continue; 
        }
        
        printf("\n>>>>>> 接收到来自开发板 %s 的新连接 <<<<<<\n", inet_ntoa(client_addr.sin_addr));
        
        char pcm_path[128];
        sprintf(pcm_path, "wav/%d.pcm", audio_file_counter);

        // 1. 接收 PCM 文件
        FILE *pcm_file = fopen(pcm_path, "wb");
        if (pcm_file != NULL) {
            char buffer[BUFFER_SIZE];
            int bytes_received;
            printf("步骤 1/2: 正在接收 .pcm 文件...\n");
            
            while ((bytes_received = read(client_sockfd, buffer, sizeof(buffer))) > 0) {
                 fwrite(buffer, 1, bytes_received, pcm_file);
            }
            fclose(pcm_file);
            printf("文件接收完毕，已存为 %s\n", pcm_path);
        } else {
            perror("fopen pcm_path failed");
        }
        
        // 2. 对PCM文件进行语音识别
        printf("步骤 2/3: 正在进行语音识别...\n");
        char* result_str = run_asr_on_file(&asr_data, pcm_path);

        // 3. 将结果发送回客户端
        if (result_str != NULL) {
            printf("步骤 3/3: 正在将结果 '%s' 发送回客户端...\n", result_str);
            write(client_sockfd, result_str, strlen(result_str));
            free(result_str); // 释放 run_asr_on_file 中 strdup 分配的内存
        } else {
            const char* error_msg = "服务器识别出错";
            printf("步骤 3/3: 正在将错误信息发送回客户端...\n");
            write(client_sockfd, error_msg, strlen(error_msg));
        }

        // 4. 关闭当前连接并增加计数器
        close(client_sockfd);
        printf("连接已关闭，服务器继续等待下一个连接...\n");
        audio_file_counter++; // 为下一个文件准备
    }

exit:
    MSPLogout();
	close(server_sockfd);
    printf("服务器已关闭。\n");
    return 0;
} 