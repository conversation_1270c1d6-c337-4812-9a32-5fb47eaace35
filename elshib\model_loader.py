import cv2
import os
import requests
from tqdm import tqdm

def download_file(url, filename, description):
    """
    使用tqdm进度条下载文件。
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()  # 如果请求失败则引发HTTPError

        total_size_in_bytes = int(response.headers.get('content-length', 0))
        block_size = 1024  # 1 KB

        progress_bar = tqdm(total=total_size_in_bytes, unit='iB', unit_scale=True, desc=description)
        with open(filename, 'wb') as file:
            for data in response.iter_content(block_size):
                progress_bar.update(len(data))
                file.write(data)
        progress_bar.close()

        if total_size_in_bytes != 0 and progress_bar.n != total_size_in_bytes:
            print(f"错误: 文件 {filename} 下载不完整。")
            return False
        return True
    except requests.exceptions.RequestException as e:
        print(f"错误: 下载 {filename} 失败 - {e}")
        if os.path.exists(filename):
            os.remove(filename) # 删除不完整的文件
        return False

def get_face_detector(confidence_threshold=0.5):
    """
    检查DNN模型文件是否存在，如果不存在则下载。
    加载并返回网络模型。
    """
    PROTO_TXT = "deploy.prototxt"
    CAFFE_MODEL = "res10_300x300_ssd_iter_140000.caffemodel"

    # 模型文件的URL
    proto_url = "https://raw.githubusercontent.com/opencv/opencv/master/samples/dnn/face_detector/deploy.prototxt"
    model_url = "https://raw.githubusercontent.com/opencv/opencv_3rdparty/dnn_samples_face_detector_20170830/res10_300x300_ssd_iter_140000.caffemodel"

    if not os.path.exists(PROTO_TXT):
        print(f"模型定义文件 {PROTO_TXT} 不存在，开始下载...")
        if not download_file(proto_url, PROTO_TXT, "下载 deploy.prototxt"):
            raise IOError(f"无法下载必需的模型文件: {PROTO_TXT}")

    if not os.path.exists(CAFFE_MODEL):
        print(f"模型权重文件 {CAFFE_MODEL} 不存在，开始下载 (约10MB)...")
        if not download_file(model_url, CAFFE_MODEL, "下载 caffe model"):
            raise IOError(f"无法下载必需的模型文件: {CAFFE_MODEL}")

    print("正在从磁盘加载DNN人脸检测器...")
    net = cv2.dnn.readNetFromCaffe(PROTO_TXT, CAFFE_MODEL)
    return net, confidence_threshold 