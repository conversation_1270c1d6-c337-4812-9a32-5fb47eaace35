//客户端发送wav文件,接收xml文件，获取id的值
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <linux/input.h>
#include <strings.h>
#include "libxml/xmlmemory.h"
#include "libxml/parser.h"
#include <sys/mman.h>

int *lcd_map =NULL;
int lcd_fd;
int bmp_fd;
int ts_fd;
int x,y;
struct input_event ts_buf;
xmlChar *key, *id;
char bmpk[4][50] = {"./pic/kq.bmp","./pic/bbl.bmp","./pic/qq.bmp","./pic/123.bmp"};

//编译命令：arm-linux-gcc 11252.c -o 11252 -I /home/<USER>/xml/xml_set/include/libxml2/ -L /home/<USER>/xml/xml_set/lib/ -lxml2

//传入的cur == object作为根节点
xmlChar *__get_cmd_id(xmlDocPtr doc, xmlNodePtr cur)
{
	
	
	cur = cur->xmlChildrenNode;
	
	while (cur != NULL)
	{
		//查找到cmd子节点
	    if ((!xmlStrcmp(cur->name, (const xmlChar *)"cmd")))
	    {		
			//取出内容
			key = xmlNodeGetContent(cur);
			
		    printf("cmd: %s\n", key);
		    xmlFree(key);

			//读取节点属性
		    id = xmlGetProp(cur, (const xmlChar *)"id");
		    printf("id: %s\n", id);

		    xmlFree(doc);
		    return id;
 	    }
		cur = cur->next;
	}
	//释放文档指针
	xmlFree(doc);
    return NULL;
}

xmlChar *parse_xml(char *xmlfile)
{
	xmlDocPtr doc;
	xmlNodePtr cur1, cur2;

	//分析一个xml文件，并返回一个xml文档的对象指针： 也就是指向树
	doc = xmlParseFile(xmlfile);
	if (doc == NULL)
	{
		fprintf(stderr,"Document not parsed successfully. \n");
		return NULL;
	}
	
	//获得文档的根节点
	cur1 = xmlDocGetRootElement(doc);
	if(cur1 == NULL)
	{
		fprintf(stderr,"empty document\n");
		xmlFreeDoc(doc);
		return NULL;
	}
	//检查根节点的名称是否为nlp
	if(xmlStrcmp(cur1->name, (const xmlChar *)"nlp"))
	{
		fprintf(stderr,"document of the wrong type, root node != nlp");
		xmlFreeDoc(doc);
		return NULL;
	}
	
	//获取子元素节点
	cur1 = cur1->xmlChildrenNode;

	while (cur1 != NULL)
	{
		//检查子元素是否为result
		if ((!xmlStrcmp(cur1->name, (const xmlChar *)"result")))
		{
			//得到result的子节点
			cur2 = cur1->xmlChildrenNode;
			while(cur2 != NULL)
			{
				//查找到准确率
				if((!xmlStrcmp(cur2->name, (const xmlChar *)"confidence")))
				{			
					xmlChar *key = xmlNodeGetContent(cur2);				
					printf("confidence: %s\n",key);
					
					//若准确率低于30，则放弃当前识别
					if(atoi((char *)key) <30)
					{
						xmlFree(doc);
						fprintf(stderr, "sorry, I'm NOT sure what you say.\n");
						return NULL;
					}
				}
				
				//查找到object，则执行提取字符串及属性
				if((!xmlStrcmp(cur2->name, (const xmlChar *)"object")))
				{
					return __get_cmd_id(doc, cur2);
				}
				cur2 = cur2->next;
			}
		}
		cur1 = cur1->next;
	}

	//释放文档指针
	xmlFreeDoc(doc);
	return NULL;
}

int lcd_open(void)
{
	//打开lcd屏幕
	lcd_fd = open("/dev/fb0", O_RDWR);
	if(-1 == lcd_fd)
	{
		perror("open lcd_fd failed!\n");
		return -1;
	}
	else
	{
		//printf("open lcd_fd success!\n");
	}
	//申请虚拟内存
	lcd_map = mmap(NULL, 800*480*4, PROT_READ | PROT_WRITE, MAP_SHARED, lcd_fd, 0);
}

int show_bmp(char *picname, int x0, int y0)
{
	
	//2.打开bmp图片
	
	bmp_fd = open(picname, O_RDWR);
	if(-1 == bmp_fd)
	{
		perror("open bmp failed!\n");
	}
	else
	{
		printf("open bmp success!\n");
	}
	//获取图片的宽和高
	char head[54];
	read(bmp_fd, head, 54);
	int w,h;
	w = *((int *)&head[18]);
	h = *((int *)&head[22]);
	printf("w=%d,h=%d\n",w,h);
	//获取图片的像素数据
	
	//偏移54个字节
	lseek(bmp_fd, 54, SEEK_SET);
	char bmp_buf[w*h*3];//24
	read(bmp_fd, bmp_buf, sizeof(bmp_buf));

	//把24位图片像素数据转换成32位
	int bmp32[w*h];//32
	int i;
	for(i=0;i<w*h;i++)
	{
		/*bmp32[0+i*4]=bmp_buf[0+i*3];//b
		bmp32[1+i*4]=bmp_buf[1+i*3];//g
		bmp32[2+i*4]=bmp_buf[2+i*3];//r
		bmp32[3+i*4]=0x00;//a*/
		//b\g\r\a,
		bmp32[i]=bmp_buf[0+i*3]<<0 |
		bmp_buf[1+i*3]<<8 | 
		bmp_buf[2+i*3]<<16 | 
		0x00<<24;
	}
	
	//图片翻转,将图片的像素数据写入lcd
	
	int x,y;
	for(y=0;y<h;y++)
	{
		for(x=0;x<w;x++)
		{
			lcd_map[800*(y0+y)+x0+x]=bmp32[w*(h-1-y)+x];
		}
	}
	
	return 0;
}


int allclose(void)
{
	close(lcd_fd);
	close(bmp_fd);
	munmap(lcd_map,800*480*4);
	close(ts_fd);
}

int init_sock(void)
{
	//创建套接字
	int soc_fd;
	soc_fd = socket(AF_INET, SOCK_STREAM, 0);
	if(-1 == soc_fd)
	{
		perror("socket failed!\n");
		return -1;
	}
	
	//地址可重用
	int on = 1;
	setsockopt(soc_fd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(1));
	
	//发起连接请求
	printf("发起连接请求\n");
	struct sockaddr_in jack;
	jack.sin_family = AF_INET;
	jack.sin_port = htons(54321);
	jack.sin_addr.s_addr = inet_addr("169.254.242.123");
	int con_fd;
	socklen_t len = sizeof(struct sockaddr);
	con_fd = connect(soc_fd, (struct sockaddr*)&jack, len);
	if(-1 == con_fd)
	{
		perror("connect failed!\n");
		return -1;
	}
	return soc_fd;
}


int ts_open(void)
{
	//打开触摸屏驱动
	ts_fd = open("/dev/input/event0", O_RDWR);
	if(-1 == ts_fd)
	{
		perror("open ts failed!\n");
		return -1;
	}
}

int get_xy(int *x, int *y)
{
	//获取触摸屏坐标
	bzero(&ts_buf, sizeof(ts_buf));
	while(1)
	{
		read(ts_fd, &ts_buf, sizeof(ts_buf));
		if(ts_buf.type == EV_ABS&&ts_buf.code == ABS_X)
		{
			*x = ts_buf.value*800/1024;
		}
		if(ts_buf.type == EV_ABS&&ts_buf.code == ABS_Y)
		{
			*y = ts_buf.value*480/600;
		}
		if(ts_buf.type == EV_KEY&&ts_buf.code == BTN_TOUCH&&ts_buf.value==0)
		{
			break;
		}
	}
}

int sendfile(int soc_fd)
{
	//传输文件
	int wav_fd;
	wav_fd = open("./example.wav", O_RDWR);
	if(-1 == wav_fd)
	{
		perror("open wav failed!\n");
		return -1;
	}
	char buf[1024];
	int ret;
	int sum=0;
	while(1)
	{
		ret = read(wav_fd, buf, 1024);
		if(-1 == ret)
		{
			perror("read failed!\n");
			return -1;
		}
		else if(0 == ret)
		{
			break;
		}
		else
		{
			sum+=ret;
			write(soc_fd, buf, ret);
		}
	}
	
	printf("写入了%d个字节",sum);
	close(wav_fd);
	printf("传输成功\n");
	
}


//接收xml文件
int recefile(int soc_fd)
{
	char xml_buf[1024];
	int xml_fd;
	xml_fd = open("./result.xml", O_RDWR|O_CREAT|O_TRUNC, 0666);
	if(-1 == xml_fd)
	{
		perror("open xml failed!\n");
		return -1;
	}
	int ret;
	ret = read(soc_fd, xml_buf, 1024);
	write(xml_fd, xml_buf, ret);
	close(xml_fd);
}


int main()
{
	//初始化网络
	int soc_fd = init_sock();
	ts_open();
	int n=0;
	while(1)
	{
		printf("点击屏幕左侧开始录音\n");
		get_xy(&x,&y);
		if(x<400)
		{
			system("arecord -d3 -c1 -r16000 -twav -fS16_LE example.wav");
			sendfile(soc_fd);//发送文件。wav
			recefile(soc_fd);//接受文件。xml
			//获取id的值
			xmlChar *id = parse_xml("result.xml");
			if(atoi((char *)id)==1)
			{
				printf("id=%s\n",id);
				system("mplayer 1.mp3");//播放MP3
			}
			else if(atoi((char *)id)==2)//显示图片
			{
				printf("id=%s\n",id);
				system("./11184");
			}
			/*else if(atoi((char *)id)==3)
			{
				n=n-1;
				if(n<0)
				{
					n = 3;
				}
				show_bmp(bmpk[n],0,0);
			}
			else if(atoi((char *)id)==4)
			{
				n=n+1;
				if(n>3)
				{
					n = 0;
				}
				show_bmp(bmpk[n],0,0);
			}*/
		}
		else
		{
			break;
		}
		
	}
	
	
	
	return 0;
}