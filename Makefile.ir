CC = gcc
CFLAGS = -Wall -pthread
TARGETS = ir_detector ir_monitor ir_demo

all: $(TARGETS)

# 简单的红外检测器
ir_detector: ir_detector.c
	$(CC) $(CFLAGS) -o $@ $<

# 红外监控系统（带LED和蜂鸣器）
ir_monitor: ir_monitor.c
	$(CC) $(CFLAGS) -o $@ $<

# 使用模块化设计的红外演示
ir_module.o: ir_module.c ir_module.h
	$(CC) $(CFLAGS) -c -o $@ $<

ir_demo: ir_demo.c ir_module.o
	$(CC) $(CFLAGS) -o $@ $^ 

clean:
	rm -f $(TARGETS) *.o

.PHONY: all clean